﻿// Copyright (c) FieldAssist. All Rights Reserved.
using System.Collections.Generic;

namespace Libraries.CommonEnums.Helpers
{
    public class OrderSummaryPdfConfigField
    {
        public static List<OrderSummaryPdfConfigFields> OrderSummaryPdfFields =>
            new()
            {
                // Company Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Logo",
                    Field = OrderSummPdfFields.CompanyLogo,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 1
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Name",
                    Field = OrderSummPdfFields.CompanyName,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 2
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company GSTIN",
                    Field = OrderSummPdfFields.CompanyGSTIN,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 3
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Tax ID",
                    Field = OrderSummPdfFields.CompanyTaxID,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 4
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Address",
                    Field = OrderSummPdfFields.Address,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 5
                },
                // Order Summary.
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Summary",
                    Field = OrderSummPdfFields.OrderSummary,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 6
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Date",
                    Field = OrderSummPdfFields.OrderDate,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 7
                },
                // Distributor Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Name",
                    Field = OrderSummPdfFields.DistributorName,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 8
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Number",
                    Field = OrderSummPdfFields.DistributorNumber,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 9
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Address",
                    Field = OrderSummPdfFields.DistributorAddress,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 10
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor GSTIN",
                    Field = OrderSummPdfFields.DistributorGSTIN,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 11
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Tax ID",
                    Field = OrderSummPdfFields.DistributorTaxId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 12
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Erp ID",
                    Field = OrderSummPdfFields.DistributorErpId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 13
                },
                // Retailer Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Name",
                    Field = OrderSummPdfFields.RetailerName,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 14
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Number",
                    Field = OrderSummPdfFields.RetailerNumber,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 15
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Address",
                    Field = OrderSummPdfFields.RetailerAddress,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 16
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer GSTIN",
                    Field = OrderSummPdfFields.RetailerGSTIN,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 17
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Tax ID",
                    Field = OrderSummPdfFields.RetailerTaxID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 18
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Erp ID",
                    Field = OrderSummPdfFields.RetailerErpID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 19
                },
                // User Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Name",
                    Field = OrderSummPdfFields.UserName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 20
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Contact Number",
                    Field = OrderSummPdfFields.UserNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 21
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Erp ID",
                    Field = OrderSummPdfFields.UserErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 22
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Beat Details",
                    Field = OrderSummPdfFields.BeatDetails,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 23
                },
                // Order Summary Grid
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Name",
                    Field = OrderSummPdfFields.ProductName,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 24
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Code",
                    Field = OrderSummPdfFields.ProductCode,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 25
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "HSN Number",
                    Field = OrderSummPdfFields.HSNNumber,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 26
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Unit and Std Unit)",
                    Field = OrderSummPdfFields.Quantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 27
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Super Unit)",
                    Field = OrderSummPdfFields.SuperUnits,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 28
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Return Quantity",
                    Field = OrderSummPdfFields.ReturnQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 29
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "MRP (per unit)",
                    Field = OrderSummPdfFields.MRPPerUnit,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 30
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Rate per unit (PTR)",
                    Field = OrderSummPdfFields.RatePerUnitPTR,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 31
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Quantity)",
                    Field = OrderSummPdfFields.FreeQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 32
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Amount)",
                    Field = OrderSummPdfFields.FOCValue,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 33
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount Amount",
                    Field = OrderSummPdfFields.DiscountAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 34
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST %",
                    Field = OrderSummPdfFields.GSTPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 35
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST Amount",
                    Field = OrderSummPdfFields.GSTAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 36
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax %",
                    Field = OrderSummPdfFields.TaxPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 37
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax Amount",
                    Field = OrderSummPdfFields.TaxAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 38
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Cess Amount",
                    Field = OrderSummPdfFields.CessAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 39
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount",
                    Field = OrderSummPdfFields.TotalValueInclGST,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 40
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount After Discount",
                    Field = OrderSummPdfFields.TotalValueAfterDiscount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 41
                },
                
                // Summary Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Gross Amount",
                    Field = OrderSummPdfFields.GrossAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 42
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CGST",
                    Field = OrderSummPdfFields.CGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 43
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "SGST",
                    Field = OrderSummPdfFields.SGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 44
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "IGST",
                    Field = OrderSummPdfFields.IGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 45
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Tax Amount",
                    Field = OrderSummPdfFields.TaxableAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 46
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Cess Amount",
                    Field = OrderSummPdfFields.TotalCessAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 47
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total FOC  Amount",
                    Field = OrderSummPdfFields.TotalFOCValue,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 48
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Discount",
                    Field = OrderSummPdfFields.Discount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 49
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Benefits",
                    Field = OrderSummPdfFields.TotalBenefits,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 50
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Net Amount",
                    Field = OrderSummPdfFields.TotalAmountInclTaxes,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 51
                },
                // Remarks
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Remarks",
                    Field = OrderSummPdfFields.Remarks,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 52
                }
            };
        public static List<OrderSummaryPdfConfigFields> VanOrderPdfFields =>
            new()
            {
                // Company Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Logo",
                    Field = OrderSummPdfFields.CompanyLogo,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 1
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Name",
                    Field = OrderSummPdfFields.CompanyName,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 2
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company GSTIN",
                    Field = OrderSummPdfFields.CompanyGSTIN,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 3
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Tax ID",
                    Field = OrderSummPdfFields.CompanyTaxID,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 4
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Address",
                    Field = OrderSummPdfFields.Address,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 5
                },
                // Order Summary.
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Summary",
                    Field = OrderSummPdfFields.OrderSummary,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 6
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Date",
                    Field = OrderSummPdfFields.OrderDate,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 7
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Invoice Number",
                    Field = OrderSummPdfFields.InvoiceNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 8
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Serial Number",
                    Field = OrderSummPdfFields.SerialNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 9
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CU Invoice Number",
                    Field = OrderSummPdfFields.CUInvoiceNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 10
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "QR Code",
                    Field = OrderSummPdfFields.QRCode,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 11
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Expected Delivery Date",
                    Field = OrderSummPdfFields.ExpectedDeliveryDate,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 80
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Remarks",
                    Field = OrderSummPdfFields.OrderRemark,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 87
                },
                // Distributor Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Name",
                    Field = OrderSummPdfFields.DistributorName,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 12
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Number",
                    Field = OrderSummPdfFields.DistributorNumber,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 13
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Address",
                    Field = OrderSummPdfFields.DistributorAddress,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 14
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor GSTIN",
                    Field = OrderSummPdfFields.DistributorGSTIN,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 15
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Tax ID",
                    Field = OrderSummPdfFields.DistributorTaxId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 16
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Erp ID",
                    Field = OrderSummPdfFields.DistributorErpId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 17
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 1",
                    Field = OrderSummPdfFields.DistributorAttributeText1,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 18
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 2",
                    Field = OrderSummPdfFields.DistributorAttributeText2,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 19
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 3",
                    Field = OrderSummPdfFields.DistributorAttributeText3,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 20
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Number 1",
                    Field = OrderSummPdfFields.DistributorAttributeNumber1,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 21
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Number 2",
                    Field = OrderSummPdfFields.DistributorAttributeNumber2,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 22
                },
                // Retailer Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Name",
                    Field = OrderSummPdfFields.RetailerName,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 23
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Number",
                    Field = OrderSummPdfFields.RetailerNumber,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 24
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Address",
                    Field = OrderSummPdfFields.RetailerAddress,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 25
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer GSTIN",
                    Field = OrderSummPdfFields.RetailerGSTIN,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 26
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Tax ID",
                    Field = OrderSummPdfFields.RetailerTaxID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 27
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Erp ID",
                    Field = OrderSummPdfFields.RetailerErpID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 28
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 1",
                    Field = OrderSummPdfFields.AttributeText1,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 29
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 2",
                    Field = OrderSummPdfFields.AttributeText2,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 30
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 3",
                    Field = OrderSummPdfFields.AttributeText3,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 31
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 1 ",
                    Field = OrderSummPdfFields.AttributeNumber1,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 32
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 2 ",
                    Field = OrderSummPdfFields.AttributeNumber2,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 33
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 3 ",
                    Field = OrderSummPdfFields.AttributeNumber3,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 34
                },
                // User Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Field User Name",
                    Field = OrderSummPdfFields.UserName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 35
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Field User Contact No",
                    Field = OrderSummPdfFields.UserNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 36
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Field User ERP ID",
                    Field = OrderSummPdfFields.UserErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 37
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Beat Details",
                    Field = OrderSummPdfFields.BeatDetails,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 38
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Vehicle Number",
                    Field = OrderSummPdfFields.VehicleNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 39
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Driver Name",
                    Field = OrderSummPdfFields.DriverName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 40
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Driver Contact Number",
                    Field = OrderSummPdfFields.DriverNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 41
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Vehicle Erp ID",
                    Field = OrderSummPdfFields.VehicleErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 42
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Text 1",
                    Field = OrderSummPdfFields.EmployeeAttributeText1,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 43
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Text 2",
                    Field = OrderSummPdfFields.EmployeeAttributeText2,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 44
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Number 1",
                    Field = OrderSummPdfFields.EmployeeAttributeNumber1,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 45
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Number 2",
                    Field = OrderSummPdfFields.EmployeeAttributeNumber2,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 46
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Van Chassis No",
                    Field = OrderSummPdfFields.ChassisNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 76
                },
                // Order Summary Grid
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Name",
                    Field = OrderSummPdfFields.ProductName,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 47
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Code",
                    Field = OrderSummPdfFields.ProductCode,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 48
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "HSN Number",
                    Field = OrderSummPdfFields.HSNNumber,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 49
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Unit and Std Unit)",
                    Field = OrderSummPdfFields.Quantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 50
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Super Unit)",
                    Field = OrderSummPdfFields.SuperUnits,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 51
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Return Quantity",
                    Field = OrderSummPdfFields.ReturnQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 52
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "MRP (per unit)",
                    Field = OrderSummPdfFields.MRPPerUnit,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 53
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Rate per unit (PTR)",
                    Field = OrderSummPdfFields.RatePerUnitPTR,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 54
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Quantity)",
                    Field = OrderSummPdfFields.FreeQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 55
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Amount)",
                    Field = OrderSummPdfFields.FOCValue,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 56
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount Amount",
                    Field = OrderSummPdfFields.DiscountAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 57
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST %",
                    Field = OrderSummPdfFields.GSTPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 58
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST Amount",
                    Field = OrderSummPdfFields.GSTAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 59
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax %",
                    Field = OrderSummPdfFields.TaxPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 60
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax Amount",
                    Field = OrderSummPdfFields.TaxAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 61
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Cess Amount",
                    Field = OrderSummPdfFields.CessAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 62
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount",
                    Field = OrderSummPdfFields.TotalValueInclGST,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 63
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount After Discount",
                    Field = OrderSummPdfFields.TotalValueAfterDiscount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 64
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Net Amount",
                    Field = OrderSummPdfFields.NetValue,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 84
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount %",
                    Field = OrderSummPdfFields.OrderDiscountPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 88
                },
                // Summary Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Gross Amount",
                    Field = OrderSummPdfFields.GrossAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 65
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CGST",
                    Field = OrderSummPdfFields.CGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 66
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "SGST",
                    Field = OrderSummPdfFields.SGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 67
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "IGST",
                    Field = OrderSummPdfFields.IGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 68
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Tax Amount",
                    Field = OrderSummPdfFields.TaxableAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 69
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Cess Amount",
                    Field = OrderSummPdfFields.TotalCessAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 70
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total FOC Amount",
                    Field = OrderSummPdfFields.TotalFOCValue,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 71
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Discount",
                    Field = OrderSummPdfFields.Discount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 72
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Benefits",
                    Field = OrderSummPdfFields.TotalBenefits,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 73
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Net Amount",
                    Field = OrderSummPdfFields.TotalAmountInclTaxes,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 74
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST",
                    Field = OrderSummPdfFields.GST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 77
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Excise",
                    Field = OrderSummPdfFields.TotalExcise,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 78
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Amount After Discount",
                    Field = OrderSummPdfFields.AmountAfterDiscount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 79
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Gross Total (Currency Code)",
                    Field = OrderSummPdfFields.GrossTotalCurrencyCode,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 85
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Currency Conversion Factor",
                    Field = OrderSummPdfFields.CurrencyConversionFactor,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 86
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount %",
                    Field = OrderSummPdfFields.DiscountPercent,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 89
                },
                // Remarks
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Remarks",
                    Field = OrderSummPdfFields.Remarks,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 75
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Salesman Name",
                    Field = OrderSummPdfFields.SalesmanName,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 81
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Signature Section",
                    Field = OrderSummPdfFields.RetailerSignature,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 82
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Terms & Conditions",
                    Field = OrderSummPdfFields.TermsAndConditions,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 83
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "*** Thank you for your purchase! ***",
                    Field = OrderSummPdfFields.Salutation,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 90
                }
            };
        public static List<OrderSummaryPdfConfigFields> DistributorOrderPdfFields =>
            new()
            {
                // Company Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Logo",
                    Field = OrderSummPdfFields.CompanyLogo,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 1
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Name",
                    Field = OrderSummPdfFields.CompanyName,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 2
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company GSTIN",
                    Field = OrderSummPdfFields.CompanyGSTIN,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 3
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Tax ID",
                    Field = OrderSummPdfFields.CompanyTaxID,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 4
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Address",
                    Field = OrderSummPdfFields.Address,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 5
                },
                // Order Summary.
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Summary",
                    Field = OrderSummPdfFields.OrderSummary,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 6
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Date",
                    Field = OrderSummPdfFields.OrderDate,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 7
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Invoice Number",
                    Field = OrderSummPdfFields.InvoiceNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 8
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Serial Number",
                    Field = OrderSummPdfFields.SerialNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 9
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CU Invoice Number",
                    Field = OrderSummPdfFields.CUInvoiceNumber,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 10
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "QR Code",
                    Field = OrderSummPdfFields.QRCode,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 11
                },
                // Distributor Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Name",
                    Field = OrderSummPdfFields.DistributorName,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 12
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Number",
                    Field = OrderSummPdfFields.DistributorNumber,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 13
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Address",
                    Field = OrderSummPdfFields.DistributorAddress,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 14
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor GSTIN",
                    Field = OrderSummPdfFields.DistributorGSTIN,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 15
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Tax ID",
                    Field = OrderSummPdfFields.DistributorTaxId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 16
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Erp ID",
                    Field = OrderSummPdfFields.DistributorErpId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 17
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 1",
                    Field = OrderSummPdfFields.DistributorAttributeText1,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 18
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 2",
                    Field = OrderSummPdfFields.DistributorAttributeText2,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 19
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Text 3",
                    Field = OrderSummPdfFields.DistributorAttributeText3,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 20
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Number 1",
                    Field = OrderSummPdfFields.DistributorAttributeNumber1,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 21
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Attribute Number 2",
                    Field = OrderSummPdfFields.DistributorAttributeNumber2,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 22
                },
                // Retailer Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Name",
                    Field = OrderSummPdfFields.RetailerName,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 23
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Number",
                    Field = OrderSummPdfFields.RetailerNumber,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 24
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Address",
                    Field = OrderSummPdfFields.RetailerAddress,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 25
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer GSTIN",
                    Field = OrderSummPdfFields.RetailerGSTIN,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 26
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Tax ID",
                    Field = OrderSummPdfFields.RetailerTaxID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 27
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Retailer Erp ID",
                    Field = OrderSummPdfFields.RetailerErpID,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 28
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 1",
                    Field = OrderSummPdfFields.AttributeText1,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 29
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 2",
                    Field = OrderSummPdfFields.AttributeText2,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 30
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Text 3",
                    Field = OrderSummPdfFields.AttributeText3,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 31
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 1 ",
                    Field = OrderSummPdfFields.AttributeNumber1,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 32
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 2 ",
                    Field = OrderSummPdfFields.AttributeNumber2,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 33
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Attribute Number 3 ",
                    Field = OrderSummPdfFields.AttributeNumber3,
                    SectionType = OrderSummPdfSections.RetailerDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 34
                },
                // User Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Name",
                    Field = OrderSummPdfFields.UserName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 35
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Contact Number",
                    Field = OrderSummPdfFields.UserNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 36
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Erp ID",
                    Field = OrderSummPdfFields.UserErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 37
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Beat Details",
                    Field = OrderSummPdfFields.BeatDetails,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 38
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Vehicle Number",
                    Field = OrderSummPdfFields.VehicleNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 39
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Driver Name",
                    Field = OrderSummPdfFields.DriverName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 40
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Driver Number",
                    Field = OrderSummPdfFields.DriverNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 41
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Vehicle Erp ID",
                    Field = OrderSummPdfFields.VehicleErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 42
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Text 1",
                    Field = OrderSummPdfFields.EmployeeAttributeText1,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 43
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Text 2",
                    Field = OrderSummPdfFields.EmployeeAttributeText2,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 44
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Number 1",
                    Field = OrderSummPdfFields.EmployeeAttributeNumber1,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 45
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Employee Attribute Number 2",
                    Field = OrderSummPdfFields.EmployeeAttributeNumber2,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 46
                },
                // Order Summary Grid
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Name",
                    Field = OrderSummPdfFields.ProductName,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 47
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Code",
                    Field = OrderSummPdfFields.ProductCode,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 48
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "HSN Number",
                    Field = OrderSummPdfFields.HSNNumber,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 49
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Unit and Std Unit)",
                    Field = OrderSummPdfFields.Quantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 50
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Super Unit)",
                    Field = OrderSummPdfFields.SuperUnits,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 51
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Return Quantity",
                    Field = OrderSummPdfFields.ReturnQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 52
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "MRP (per unit)",
                    Field = OrderSummPdfFields.MRPPerUnit,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 53
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Rate per unit (PTR)",
                    Field = OrderSummPdfFields.RatePerUnitPTR,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 54
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Quantity)",
                    Field = OrderSummPdfFields.FreeQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 55
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Amount)",
                    Field = OrderSummPdfFields.FOCValue,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 56
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount Amount",
                    Field = OrderSummPdfFields.DiscountAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 57
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST %",
                    Field = OrderSummPdfFields.GSTPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 58
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST Amount",
                    Field = OrderSummPdfFields.GSTAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 59
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax %",
                    Field = OrderSummPdfFields.TaxPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 60
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax Amount",
                    Field = OrderSummPdfFields.TaxAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 61
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Cess Amount",
                    Field = OrderSummPdfFields.CessAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 62
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount",
                    Field = OrderSummPdfFields.TotalValueInclGST,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 63
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount After Discount",
                    Field = OrderSummPdfFields.TotalValueAfterDiscount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 64
                },
                // Summary Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Gross Amount",
                    Field = OrderSummPdfFields.GrossAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 65
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CGST",
                    Field = OrderSummPdfFields.CGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 66
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "SGST",
                    Field = OrderSummPdfFields.SGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 67
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "IGST",
                    Field = OrderSummPdfFields.IGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 68
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Tax Amount",
                    Field = OrderSummPdfFields.TaxableAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 69
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Cess Amount",
                    Field = OrderSummPdfFields.TotalCessAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 70
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total FOC Amount",
                    Field = OrderSummPdfFields.TotalFOCValue,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 71
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Discount",
                    Field = OrderSummPdfFields.Discount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 72
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Benefits",
                    Field = OrderSummPdfFields.TotalBenefits,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 73
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Net Amount",
                    Field = OrderSummPdfFields.TotalAmountInclTaxes,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 74
                },
                // Remarks
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Remarks",
                    Field = OrderSummPdfFields.Remarks,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 75
                }
            };
        public static List<OrderSummaryPdfConfigFields> PrimaryOrderPdfFields =>
            new()
            {
                // Company Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Logo",
                    Field = OrderSummPdfFields.CompanyLogo,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 1
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Name",
                    Field = OrderSummPdfFields.CompanyName,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 2
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company GSTIN",
                    Field = OrderSummPdfFields.CompanyGSTIN,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 3
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Tax ID",
                    Field = OrderSummPdfFields.CompanyTaxID,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 4
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Company Address",
                    Field = OrderSummPdfFields.Address,
                    SectionType = OrderSummPdfSections.CompanyDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 5
                },
                // Order Summary.
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Summary",
                    Field = OrderSummPdfFields.OrderSummary,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 6
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Order Date",
                    Field = OrderSummPdfFields.OrderDate,
                    SectionType = OrderSummPdfSections.OrderSummary,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 7
                },
                // Distributor Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Name",
                    Field = OrderSummPdfFields.DistributorName,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 8
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Number",
                    Field = OrderSummPdfFields.DistributorNumber,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 9
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Address",
                    Field = OrderSummPdfFields.DistributorAddress,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 10
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor GSTIN",
                    Field = OrderSummPdfFields.DistributorGSTIN,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 11
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Tax ID",
                    Field = OrderSummPdfFields.DistributorTaxId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 12
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Distributor Erp ID",
                    Field = OrderSummPdfFields.DistributorErpId,
                    SectionType = OrderSummPdfSections.DistributorDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 13
                },
                // User Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Name",
                    Field = OrderSummPdfFields.UserName,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 14
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Contact Number",
                    Field = OrderSummPdfFields.UserNumber,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 15
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "User Erp ID",
                    Field = OrderSummPdfFields.UserErpID,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 16
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Beat Details",
                    Field = OrderSummPdfFields.BeatDetails,
                    SectionType = OrderSummPdfSections.UserDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 17
                },
                // Order Summary Grid
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Name",
                    Field = OrderSummPdfFields.ProductName,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 18
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Product Code",
                    Field = OrderSummPdfFields.ProductCode,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 19
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "HSN Number",
                    Field = OrderSummPdfFields.HSNNumber,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 20
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Unit and Std Unit)",
                    Field = OrderSummPdfFields.Quantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 21
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Quantity (Super Unit)",
                    Field = OrderSummPdfFields.SuperUnits,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 22
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Return Quantity",
                    Field = OrderSummPdfFields.ReturnQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 23
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "MRP (per unit)",
                    Field = OrderSummPdfFields.MRPPerUnit,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 24
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Rate per unit (PTR)",
                    Field = OrderSummPdfFields.RatePerUnitPTR,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 25
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Quantity)",
                    Field = OrderSummPdfFields.FreeQuantity,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 26
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "FOC (Amount)",
                    Field = OrderSummPdfFields.FOCValue,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 27
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Discount Amount",
                    Field = OrderSummPdfFields.DiscountAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 28
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST %",
                    Field = OrderSummPdfFields.GSTPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 29
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "GST Amount",
                    Field = OrderSummPdfFields.GSTAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 30
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax %",
                    Field = OrderSummPdfFields.TaxPercent,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 31
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Tax Amount",
                    Field = OrderSummPdfFields.TaxAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 32
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Cess Amount",
                    Field = OrderSummPdfFields.CessAmount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 33
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount",
                    Field = OrderSummPdfFields.TotalValueInclGST,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 34
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Amount After Discount",
                    Field = OrderSummPdfFields.TotalValueAfterDiscount,
                    SectionType = OrderSummPdfSections.OrderSummaryGrid,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 35
                },
                // Summary Details
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Gross Amount",
                    Field = OrderSummPdfFields.GrossAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 36
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "CGST",
                    Field = OrderSummPdfFields.CGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 37
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "SGST",
                    Field = OrderSummPdfFields.SGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 38
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "IGST",
                    Field = OrderSummPdfFields.IGST,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 39
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Tax Amount",
                    Field = OrderSummPdfFields.TaxableAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 40
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Cess Amount",
                    Field = OrderSummPdfFields.TotalCessAmount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 41
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total FOC Amount",
                    Field = OrderSummPdfFields.TotalFOCValue,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 42
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Discount",
                    Field = OrderSummPdfFields.Discount,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 43
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Total Benefits",
                    Field = OrderSummPdfFields.TotalBenefits,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 44
                },
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Net Amount",
                    Field = OrderSummPdfFields.TotalAmountInclTaxes,
                    SectionType = OrderSummPdfSections.SummaryDetails,
                    IsMandatory = true,
                    IsEditable = true,
                    DisplayOrder = 45
                },
                // Remarks
                new OrderSummaryPdfConfigFields
                {
                    DisplayName = "Remarks",
                    Field = OrderSummPdfFields.Remarks,
                    SectionType = OrderSummPdfSections.Remarks,
                    IsMandatory = false,
                    IsEditable = true,
                    DisplayOrder = 46
                }
            };

    }

    public class OrderSummaryPdfConfigFields
    {
        public string DisplayName { get; set; }
        public OrderSummPdfFields Field { get; set; }
        public OrderSummPdfSections SectionType { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsEditable { get; set; }
        public int DisplayOrder { get; set; }
    }
}
