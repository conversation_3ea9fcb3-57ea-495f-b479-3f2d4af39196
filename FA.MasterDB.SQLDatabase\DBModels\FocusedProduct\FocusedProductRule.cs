﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.FocusedProduct
{
    public class FocusedProductRule
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public long ManagerId { get; set; }

        public PortalUserRole ManagerRole { get; set; }

        public long CompanyId { get; set; }

        public string DisplayCatList { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool Deleted { get; set; }

        public string SKUList { get; set; }

        public List<FocusedProductRulePositionCodeMapping> FocusedProductRulePositionCodeMappings { get; set; }

        public string ShopTypes { get; set; }
    }
}
