﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    [Table("EmployeeDailyTargets")]
    public class EmployeeDailyTargets
    {
        public long Id { get; set; }

        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public long PlanId { get; set; }

        public TargetValueTypeDashboard TargetValueType { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public virtual ClientEmployee Employee { get; set; }

        public virtual Company Company { get; set; }

        public ICollection<EmployeeDailyTargetItems> EmployeeDailyTargetItems { get; set; }
    }
}
