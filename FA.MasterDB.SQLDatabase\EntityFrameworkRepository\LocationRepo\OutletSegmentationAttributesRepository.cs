﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.Mapping;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo
{
    public class OutletSegmentationAttributesRepository : IOutletSegmentationAttributesRepository
    {
        private readonly MasterDbContext _masterDbContext;

        public OutletSegmentationAttributesRepository(MasterDbContext masterDbContext)
        {
            _masterDbContext = masterDbContext;
        }

        public async Task<List<OutletSegmentationAttributeDTO>> GetAllSegments(long companyId)
        {
            var lists = await _masterDbContext.OutletSegmentationAttributes.
                Where(s => s.CompanyId == companyId && s.IsValid)
                .Select(s =>
                OutletSegmentationAttributeMapping.CreateObject_OutletSegmentationAttribute(s))
                .ToListAsync();

            return lists;
        }

        public async Task<OutletSegmentationAttributeDTO> GetOutletSegment(long companyId,
            OutletSegmentation outletSegmentation)
        {
            var record = await _masterDbContext.OutletSegmentationAttributes.
                Where(s => s.CompanyId == companyId && s.IsValid && s.Segmentation == outletSegmentation)
                .FirstOrDefaultAsync();

            if (record != null)
            {
                var outletSegment = OutletSegmentationAttributeMapping.CreateObject_OutletSegmentationAttribute(record);
                return outletSegment;
            }
            else
            {
                return null;
            }
        }
    }
}
