﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Gamification
{
    [Table("GameAlert")]
    public class GameAlert
    {
        public int GameAlertId { get; set; }

        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long GameId { get; set; }

        public long TeamId { get; set; }

        public long KpiId { get; set; }

        public int AlertType { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
