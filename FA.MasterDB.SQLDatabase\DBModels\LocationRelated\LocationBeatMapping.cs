﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("BeatsToLocations")]
    public class LocationBeatMapping
    {
        public long Id { get; set; }

        [Column("Location")]
        public long LocationId { get; set; }

        [Column("LocationBeat")]
        public long BeatId { get; set; }
    }
}
