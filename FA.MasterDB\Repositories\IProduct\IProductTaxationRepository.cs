﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;

namespace FA.MasterDB.Core.Repositories.IProduct
{
    public interface IProductTaxationRepository
    {
        Task<List<ProductTaxInfoDTO>> GetProductTaxInfo(long companyId);
        Task<List<ProductCessInfoDTO>> GetProductCessInfo(long companyId);

        Task<List<ProductAllTaxInfoDto>> GetProductAllTaxInfo(long companyId);
    }
}
