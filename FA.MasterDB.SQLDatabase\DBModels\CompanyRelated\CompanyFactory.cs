﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    [Table("CompanyFactories")]
    public class CompanyFactory
    {
        public bool IsDeactive { get; set; }
        public bool Deleted { get; set; }

        public long CompanyId { get; set; }

        //public Company Company { get; set; }

        public long Id { get; set; }

        public Guid Guid { get; set; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }

        [StringLength(2048)]
        public string Name { get; set; }

        [StringLength(1000)]
        public string State { get; set; }

        [StringLength(1000)]
        public string City { get; set; }

        public string PhoneNumber { get; set; }

        public string EmailId { get; set; }
        public string PanNumber { get; set; }

        public string ErpId { get; set; }

        public string FssaiNo { get; set; }

        [StringLength(10000)]
        public string Address { get; set; }

        [StringLength(6)]
        public string Pincode { get; set; }

        [StringLength(50)]
        public string GSTIN { get; set; }

        public long RegionId { get; set; }

        //public Region Region { get; set; }

        public FactoryProductType ProductEntityType { get; set; }

        [StringLength(int.MaxValue)]
        public string ProductEntityIds { get; set; }

        public virtual Region Region { get; private set; }
    }
}
