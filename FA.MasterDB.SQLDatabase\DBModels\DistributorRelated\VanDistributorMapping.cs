﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class VanDistributorMapping
    {
        public long Id { get; set; }

        [ForeignKey("VanMaster")]
        public long VanId { get; set; }

        [ForeignKey("Distributor")]
        public long DistributorId { get; set; }

        public long? EmployeeId { get; set; }

        public Distributor Distributor { get; set; }

        public VanMaster VanMaster { get; set; }

        public bool IsVanUserMappingDeleted { get; set; }

        [Column("WarehouseErpId", TypeName = "nvarchar(64)")]
        public string WarehouseErpId { get; set; }
    }
}
