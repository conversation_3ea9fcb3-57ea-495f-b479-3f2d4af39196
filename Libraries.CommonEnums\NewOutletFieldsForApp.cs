﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums;

public enum NewOutletFieldsForApp
{
    MarketName = 1,
    OwnersName = 2,
    ContactNo = 3,
    District = 4,
    State = 5,
    PinCode = 6,
    Email = 7,
    GSTIN = 8,
    ShopTypeId = 9,
    Segmentation = 10,
    ChannelId = 11,
    ImageId = 12,
    PAN = 13,
    A<PERSON>har = 14,
    GSTRegistered = 15,
    BankAccountNumber = 16,
    AccountHoldersName = 17,
    IFSCCode = 18,
    PlaceOfDelivery = 19,
    AttributeText1 = 28,
    AttributeText2 = 29,
    AttributeText3 = 30,
    AttributeText4 = 31,
    AttributeNumber1 = 32,
    AttributeNumber2 = 33,
    AttributeNumber3 = 34,
    AttributeNumber4 = 35,
    AttributeBoolean1 = 36,
    AttributeBoolean2 = 37,
    AttributeDate1 = 38,
    AttributeDate2 = 39,
    AttributeImage1Id = 40,
    AttributeImage2Id = 41,
    AttributeImage3Id = 42,
    OutletName = 50,
    Address = 60,
    BeatId = 70,
    ERPId = 80,
    ConsumerType = 90,
    SegmentationScope = 100,
    City = 110,
    IsAssetPresent = 120,
    AssetCode = 130,
    AssetType = 140,
    AssetSize = 150,
    AssetDescription = 160,
    OutletPotential = 170,
    Franchise = 180,
    EntityMarginSlabId = 190,
    SecondaryEmail = 200,
    IsFocussed = 210,
    Latitude = 220,
    Longitude = 230,
    FSSAINumber = 43,
    FSSAIExpiryDate = 44,
    SubShopType = 45,
    ISRAvailability = 260,
    CustomTags = 270,
    TIN = 261
}

public enum NewOutletFieldsFor_GTApp
{
    Market = 1,
    ContactName = 2,
    ContactNo = 3,
    District = 4,
    State = 5,
    PinCode = 6,
    Email = 7,
    GSTIN = 8,
    ShopType = 9,
    Segmentation = 10,
    Channel = 11,
    Image = 12,
    PAN = 13,
    Aadhar = 14,
    GSTRegistered = 15,
    BankAccountNumber = 16,
    AccountHoldersName = 17,
    IFSCCode = 18,
    PlaceOfDelivery = 19,
    AttributeText1 = 28,
    AttributeText2 = 29,
    AttributeText3 = 30,
    AttributeText4 = 31,
    AttributeNumber1 = 32,
    AttributeNumber2 = 33,
    AttributeNumber3 = 34,
    AttributeNumber4 = 35,
    AttributeBoolean1 = 36,
    AttributeBoolean2 = 37,
    AttributeDate1 = 38,
    AttributeDate2 = 39,
    AttributeImage1 = 40,
    AttributeImage2 = 41,
    AttributeImage3 = 42,
    FSSAINumber = 43,
    FSSAIExpiryDate = 44,
    SubShopType = 45,
    OutletName = 50,
    Address = 60,
    BeatId = 70,
    ERPId = 80,
    ConsumerType = 90,
    SegmentationScope = 100,
    City = 110,
    IsAssetPresent = 120,
    AssetCode = 130,
    AssetType = 140,
    AssetSize = 150,
    AssetDescription = 160,
    OutletPotential = 170,
    Franchise = 180,
    EntityMarginSlabId = 190,
    SecondaryEmail = 200,
    IsFocussed = 210,
    Latitude = 220,
    Longitude = 230,
    ISRAvailability = 260,
    CustomTags = 270,
    TIN = 261
}

public enum CalculateOutletLocationAttributesType
{
    None = 0,
    CalculateAtNewOutlets = 1,
    CalculateAtAllOutlets = 2,
}
