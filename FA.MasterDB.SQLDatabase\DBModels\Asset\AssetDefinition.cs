﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Asset;

[Table("AssetDefinitions")]
public class AssetDefinition
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public bool IsActive { get; set; }

    public string Name { get; set; }

    public string AssetType { get; set; }

    public long ValueCapacity { get; set; }

    public long VolumeCapacity { get; set; }

    public string ShortName { get; set; }

    public string ValidationRegex { get; set; }

    public int? EntityType { get; set; }

    public string EntityIds { get; set; }

    public bool IsIRAsset { get; set; }

    public bool IsAssetRefNoScannable { get; set; }
}
