﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class BilledToShippedToAddressMapping
    {
        public static
            BilledtoshippedtoAddressInfo
            CreateObject_BilledtoshippedtoAddressInfo(
            BilledtoshippedtoAddress s)
        {
            return new BilledtoshippedtoAddressInfo()
            {
                Id = s.Id,
                Address = s.Address,
                OutletId = s.OutletId,
                IntegrationId1 = s.IntegerationId1
            };
        }
    }
}
