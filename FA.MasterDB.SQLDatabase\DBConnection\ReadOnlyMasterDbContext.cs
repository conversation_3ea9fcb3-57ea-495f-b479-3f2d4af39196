﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels;
using FA.MasterDB.SQLDatabase.DBModels.Asset;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.Coupons;
using FA.MasterDB.SQLDatabase.DBModels.Device;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated.FieldAssist.DataAccessLayer.Models.EFModels;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.DBModels.FocusedProduct;
using FA.MasterDB.SQLDatabase.DBModels.Gamification;
using FA.MasterDB.SQLDatabase.DBModels.Journey;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.OrderBlock;
using FA.MasterDB.SQLDatabase.DBModels.PendingPayments;
using FA.MasterDB.SQLDatabase.DBModels.Position;
using FA.MasterDB.SQLDatabase.DBModels.PrimarySale;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using FA.MasterDB.SQLDatabase.DBModels.Route;
using FA.MasterDB.SQLDatabase.DBModels.Schemes;
using FA.MasterDB.SQLDatabase.DBModels.Survey;
using FA.MasterDB.SQLDatabase.DBModels.TaskManagement;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.DBConnection
{
    public class CombinedMasterDbContext : DbContext
    {
        public CombinedMasterDbContext(DbContextOptions<CombinedMasterDbContext> options)
            : base(options)
        {
        }

        // ProductTable-related entities
        public DbSet<ProductTable> Products { get; set; }
        public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }
        public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }
        public DbSet<ProductDivision> ProductDivisions { get; set; }
        public DbSet<ProductSuggestiveQuantity> ProductSuggestiveQuantities { get; set; }
        public DbSet<ProductPricingMasters> ProductPricingMasters { get; set; }
        public DbSet<ProductChannelMapping> ProductChannelMappings { get; set; }
        public DbSet<ProductGSTCategory> ProductGSTCategories { get; set; }
        public DbSet<ProductCESSCategory> ProductCESSCategories { get; set; }

        // Survey-related entities
        public DbSet<Survey> Surveys { get; set; }
        public DbSet<SurveyToCompanyZoneMap> SurveyToCompanyZoneMaps { get; set; }
        public DbSet<QuestionGroup> QuestionGroups { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<Choice> Choices { get; set; }

        // Task Management entities
        public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }
        public DbSet<TaskManagementFocusArea> TaskManagementFocusAreas { get; set; }
        public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }

        // Location-related entities
        public DbSet<CountryInfo> CountryInfos { get; set; }
        public DbSet<LocationDB> Locations { get; set; }
        public DbSet<LocationBeat> LocationBeats { get; set; }
        public DbSet<LocationBeatMapping> LocationBeatMappings { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<StateWithDistrict> StateWithDistricts { get; set; }
        public DbSet<PinCodeMaster> PinCodeMasters { get; set; }
        public DbSet<GeographicalMapping> GeographicalMappings { get; set; }

        // Sales Order entities
        public DbSet<PrimarySale> PrimarySalesOrderHeaders { get; set; }
        public DbSet<PrimarySaleItems> PrimarySalesOrderDetails { get; set; }
        public DbSet<PrimarySale> PrimarySales { get; set; }
        public DbSet<PrimarySaleItems> PrimarySaleItems { get; set; }

        // Device-related entities
        public DbSet<Device> UserDevices { get; set; }
        public DbSet<Device> Devices { get; set; }
        public DbSet<Device_New> Devices_New { get; set; }

        // Employee-related entities
        public DbSet<ClientEmployee> Employees { get; set; }
        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }
        public DbSet<EmployeePjp> EmployeePJPs { get; set; }
        public DbSet<FAEmployeeBeatMappings> EmployeeBeatMappings { get; set; }
        public DbSet<EmployeeRouteMapping> EmployeeRouteMappings { get; set; }
        public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }
        public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }
        public DbSet<EmployeeDayStartRecord> EmployeeDayStartRecords { get; set; }
        public DbSet<EmployeeDailyTargets> EmployeeDailyTargets { get; set; }
        public DbSet<EmployeeToken> ClientEmployeeTokens { get; set; }
        public DbSet<Designations> Designations { get; set; }
        public DbSet<ClientEmployee> ClientEmployees { get; set; }

        // Distributor-related entities
        public DbSet<Distributor> Distributors { get; set; }
        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }
        public DbSet<DistributorRouteMapping> DistributorRouteMappings { get; set; }
        public DbSet<DistributorProductMapping> DistributorProductMappings { get; set; }
        public DbSet<DistributorFieldUserMapping> DistributorFieldUserMappings { get; set; }
        public DbSet<DistributorProductDivisionMappingDB> DistributorProductDivisionMappings { get; set; }
        public DbSet<DistributorFactoryMappings> DistributorFactoryMappings { get; set; }
        public DbSet<DistributorWiseOutletCreditLimit> DistributorWiseOutletCreditLimits { get; set; }
        public DbSet<VanDistributorMapping> VanDistributorMappings { get; set; }
        public DbSet<DistributorAddress> DistributorAddresses { get; set; }
        public DbSet<DistributorChannels> DistributorChannels { get; set; }
        public DbSet<DistributorSegmentations> DistributorSegmentations { get; set; }

        // Company-related entities
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanySetting> CompanySettings { get; set; }
        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }
        public DbSet<CompanyAppMeta> CompanyAppMetas { get; set; }
        public DbSet<CompanyDefinedProductType> CompanyDefinedProductTypes { get; set; }
        public DbSet<CompanyFactories> CompanyFactories { get; set; }
        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMappings { get; set; }
        public DbSet<CompanyNomenclature> CompanyNomenclatures { get; set; }
        public DbSet<CompanyTargetSubscriptions> CompanyTargetSubscriptions { get; set; }
        public DbSet<CompanyTargets> CompanyTargets { get; set; }
        public DbSet<CompanyAdmin> CompanyAdmins { get; set; }

        // Route-related entities
        public DbSet<RouteClass> Routes { get; set; }
        public DbSet<RoutePlan> RoutePlans { get; set; }
        public DbSet<RoutePlanItem> RoutePlanItems { get; set; }
        public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }
        public DbSet<RoutePositionMapping> RoutePositionMappings { get; set; }
        public DbSet<SecondaryRoutePlanItem> SecondaryRoutePlanItems { get; set; }
        public DbSet<RoutePlanRequest> RoutePlanRequests { get; set; }
        public DbSet<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        // Focused ProductTable Rule entity
        public DbSet<FocusedProductRule> FocusedProductRules { get; set; }

        // Cohort-related entities
        public DbSet<Cohort> Cohorts { get; set; }

        // Outlet-related entities
        public DbSet<OutletSegmentationAttribute> OutletSegmentationAttributes { get; set; }
        public DbSet<NewOutletFieldsAppMeta> NewOutletFieldsAppMetas { get; set; }
        public DbSet<OutletTag> OutletTags { get; set; }
        public DbSet<OutletMetric> OutletMetrics { get; set; }
        public DbSet<OutletwiseExternalMetricValues> OutletwiseExternalMetricValues { get; set; }
        public DbSet<GlobalOutletMetrices> GlobalOutletMetrices { get; set; }
        public DbSet<OutletOnboardingDetails> OutletOnboardingDetails { get; set; }

        // Coupon-related entities
        public DbSet<CouponsDefinition> CouponsDefinitions { get; set; }
        public DbSet<CouponScheme> CouponSchemes { get; set; }
        public DbSet<CouponSchemeSlab> CouponSchemeSlabs { get; set; }
        public DbSet<CouponMaster> CouponMasters { get; set; }
        public DbSet<SchemeCouponDistribution> SchemeCouponDistributions { get; set; }

        // Pending Payment entities
        public DbSet<FAPendingPayment> FAPendingPayments { get; set; }
        public DbSet<FAPendingPaymentInvoice> FAPendingPaymentInvoices { get; set; }

        // Team-related entities
        public DbSet<Team> Teams { get; set; }
        public DbSet<TeamUserMapping> TeamUserMappings { get; set; }

        // Gamification entities
        public DbSet<Game> Games { get; set; }
        public DbSet<GameAlert> GameAlerts { get; set; }
        public DbSet<TargetForTeams> TargetForTeams { get; set; }
        public DbSet<CoinsforKpi> CoinsforKpis { get; set; }

        // Asset-related entities
        public DbSet<AssetDefinition> AssetDefinitions { get; set; }
        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }
        public DbSet<ExternalAsset> ExternalAssets { get; set; }

        // Journey-related entities
        public DbSet<JourneyCalendar> JourneyCalendars { get; set; }
        public DbSet<JourneyCycle> JourneyCycles { get; set; }
        public DbSet<JourneyWeek> JourneyWeeks { get; set; }
        public DbSet<JourneyPlanConfigurations> JourneyPlanConfigurations { get; set; }

        // Position-related entities
        public DbSet<PositionCode> PositionCodes { get; set; }
        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }
        public DbSet<PositionCodeHierarchy> PositionCodeHierarchies { get; set; }
        public DbSet<PositionBeatMapping> PositionBeatMapping { get; set; }
        public DbSet<PositionProductDivisionMapping> PositionProductDivisionMappings { get; set; }
        public DbSet<PositionDistributorMapping> PositionDistributorMappings { get; set; }

        // Miscellaneous entities
        public DbSet<ThemeConfig> ThemeConfig { get; set; }
        public DbSet<DbExtApiToken> DbExtApiToken { get; set; }
        public DbSet<ClaimsConfiguration> ClaimsConfiguration { get; set; }
        public DbSet<DMSPOApprovalRights> DMSPOApprovalRights { get; set; }
        public DbSet<OrderBlock> OrderBlocks { get; set; }
        public DbSet<OrderBlockItems> OrderBlockItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DistributorWiseOutletCreditLimit>().ToTable("DistributorWiseOutletCreditLimit");
            modelBuilder.Entity<CompanyAppMeta>().ToTable("FACompaniesAppMeta");
            modelBuilder.Entity<CompanyNomenclatureMapping>().ToTable("FACompanyNomenclaturesMapping");
            modelBuilder.Entity<CompanyNomenclature>().ToTable("FACompanyNomenclatures");
            modelBuilder.Entity<FAEmployeeBeatMappings>().ToTable("FAEmployeeBeatMappings");
            modelBuilder.Entity<EmployeeRouteMapping>().ToTable("FAEmployeeRouteMappings");
            modelBuilder.Entity<Distributor>().ToTable(
                "FADistributors",
                t => t.HasTrigger("FADistributorsAfterUpdate"));
            modelBuilder.Entity<LocationDB>().ToTable("F2KLocations");
            modelBuilder.Entity<RegionWiseODSDB>().ToTable("RegionWiseODS");
            modelBuilder.Entity<DistributorToRetailerMargins>().ToTable("DistributorToRetailerMargins");

            // for product
            modelBuilder.Entity<ProductTable>().ToTable("FACompanyProducts");
            modelBuilder.Entity<ProductTable>().Property(b => b.DisplayMRP).HasColumnName("MRP");
            modelBuilder.Entity<FAProductBasket>().ToTable("FAProductBasket");
            modelBuilder.Entity<FABasketProductMappings>().ToTable("FABasketProductMappings");
            modelBuilder.Entity<FAProductSet>().ToTable("FAProductSet");

            // managers
            modelBuilder.Entity<ProductPrimaryCategory>().ToTable("FAProductPrimaryCategory");
            modelBuilder.Entity<ProductSecondaryCategory>().ToTable("FAProductSecondaryCategory");
            modelBuilder.Entity<ProductDivision>().ToTable("FAProductDivision");

            modelBuilder.Entity<SurveyToCompanyZoneMap>().ToTable("FASurveyToCompanyZoneMaps");
            modelBuilder.Entity<Survey>().ToTable("NewSurvey_JsonForm");
            modelBuilder.Entity<QuestionGroup>().ToTable("NewSurvey_QuestionGroup");
            modelBuilder.Entity<Question>().ToTable("NewSurvey_Question");
            modelBuilder.Entity<Choice>().ToTable("NewSurvey_QuestionChoice");

            modelBuilder.Entity<CountryInfo>().HasKey(b => b.CountryName);

            // Ignore Outlets from mapping.
            modelBuilder.Entity<Survey>().Ignore(b => b.Outlets);

            // Task-Management
            modelBuilder.Entity<TaskManagementUserFocusArea>()
                .HasOne(ufa => ufa.TaskManagementFocusAreas)
                .WithOne(fa => fa.TaskManagementUserFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementFocusArea>()
                .HasOne(fa => fa.TaskManagementUserFocusAreas)
                .WithOne(ufa => ufa.TaskManagementFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementTask>()
                .HasOne(tt => tt.TaskManagementFocusAreas)
                .WithMany(fa => fa.TaskManagementTasks)
                .HasForeignKey(tt => tt.TaskManagementFocusAreaID);

            modelBuilder.Entity<FocusedProductRule>().ToTable("FAFocusedProductRule");
        }
    }
}
