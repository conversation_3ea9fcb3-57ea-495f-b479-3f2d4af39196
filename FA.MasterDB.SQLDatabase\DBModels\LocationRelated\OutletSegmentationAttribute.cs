﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class OutletSegmentationAttribute
    {
        public long Id { get; set; }

        public int VisitsPerMonth { get; set; }

        public double MaxOutletOrderPotential { get; set; }

        public OutletSegmentation Segmentation { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public decimal MinOutletPotential { get; set; }

        public decimal MaxOutletPotential { get; set; }

        public bool IsValid { get; set; }

        public string DisplayName { get; set; }

        public string ErpId { get; set; }

        public decimal MinOutletPotentialInStdUnit { get; set; }

        public decimal MaxOutletPotentialInStdUnit { get; set; }

        public decimal MinOrderValue { get; set; }
    }
}
