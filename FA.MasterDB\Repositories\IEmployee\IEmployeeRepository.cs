﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.DTOs;

namespace FA.MasterDB.Core.Repositories.IEmployee
{
    public interface IEmployeeRepository
    {
        Task<EmployeeWithParent> GetEmployeeWithParent(long companyId, long userId);

        Task<string> GetEmployeeName(long companyId, long employeeId);

        Task<EmployeeEntity> GetEmployee(long companyId, long employeeId);
    }
}
