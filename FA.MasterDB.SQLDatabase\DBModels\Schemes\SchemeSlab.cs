﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes;

public class SchemeSlab
{
    public long Id { get; set; }

    public double Constraint { get; set; }

    public double Payout { get; set; }

    public int Priority { get; set; }

    public string PayoutDescription { get; set; }

    public long? ProductId { get; set; }

    public long SchemeId { get; set; }

    public QualifierPayoutType? QualifierPayoutType { get; set; }

    public virtual Scheme Scheme { get; set; }

    public virtual ProductTable Product { get; set; }

    public virtual ICollection<QualifierSchemeSlab> QualifierSchemeSlab { get; set; }

    public double? BasketInvoicePayout { get; set; }

    public double? MaxBasketPayout { get; set; }

    public ConstraintType? BasketConstraintType { get; set; }

    public double? NominalPrice { get; set; }

    public string BatchMasterNumber { get; set; }

    [Column("MultipleProductPayout")]
    public bool MultipleProductPayout { get; set; }

    [Column("GroupId")]
    public long? GroupId { get; set; }

    [Column("PayoutProductList")]
    public string PayoutProductList { get; set; }
}
