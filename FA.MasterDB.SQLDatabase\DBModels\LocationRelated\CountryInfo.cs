﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class CountryInfo
    {
        public int DigitsInPhNo { get; set; }
        public string CountryName { get; set; }
        public string CurrencySymbol { get; set; }
        public int TimeZoneOffsetMinutes { get; set; }
        public string CurrencyName { get; set; }
        public string Language { get; set; }
        public string PhNoPrefix { get; set; }
        public int numberSystem { get; set; }
        public int pinCodeLength { get; set; }

        [NotMapped]
        public TimeSpan TimeZoneOffset
        {
            get => TimeSpan.FromMinutes(TimeZoneOffsetMinutes);
            set => TimeZoneOffsetMinutes = (int)value.TotalMinutes;
        }
    }
}
