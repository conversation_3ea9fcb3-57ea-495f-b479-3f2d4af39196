﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using FA.MasterDB.SQLDatabase.Interfaces;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class EmployeeAdvanceLeave : ICreatedEntity
    {
        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public long Id { get; set; }

        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public DateTime Date { get; set; }

        [StringLength(1024)]
        public string Reason { get; set; }

        public bool Deleted { get; set; }

        public static EmployeeAdvanceLeave WriteFromCore(EmployeeAdvanceLeaveModel l)
        {
            return new EmployeeAdvanceLeave
            {
                CompanyId = l.CompanyId,
                Date = l.Date,
                EmployeeId = l.EmployeeId,
                Reason = l.Reason
            };
        }
    }
}
