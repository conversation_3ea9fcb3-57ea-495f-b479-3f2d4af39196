﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("OutletMetrices")]
    public class OutletMetric
    {
        public long Id { get; set; }

        public string ErpId { get; set; }

        public string Name { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public bool IsExternal { get; set; }

        public bool IsActive { get; set; }

        public long CueCardId { get; set; }

        public CueCardsMaster CueCard { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public Frequency? Frequency { get; set; }

        public DayOfWeek? WeekStartDay { get; set; }

        public long? GlobalMetricId { get; set; }

        public long? ExternalMetricId { get; set; }

        public string ParameterValue { get; set; }

        public string ColourConstraints { get; set; }

        public MetricType MetricType { get; set; }

        public string ApiValue { get; set; }

        public bool IsHidden { get; set; }

        public string CalculationLogic { get; set; }
    }
}
