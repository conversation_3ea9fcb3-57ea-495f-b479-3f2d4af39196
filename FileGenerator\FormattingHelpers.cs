﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using FileGenerator.Attributes;
using Library.StringHelpers;
using OfficeOpenXml;

namespace FileGenerator
{
    public class FormattingHelpers
    {
        public static List<ExcelConditionalFormat> ParseConditionalFormatting(string format, string separators, Dictionary<string, string> nameVsAddress = null)
        {
            var separator = separators?.FirstOrDefault() ?? ',';
            var conditions = format?.Split(separator).Select(s =>
            {
                var form = StringHelper.ReplaceStringBetweenChar('{', '}', s.Split('_')[1], nameVsAddress);
                return new ExcelConditionalFormat
                {
                    Condition = s.Split('_')[0],
                    Formula = form,
                    Color = s.Split('_')[2]
                };
            }).ToList() ?? new List<ExcelConditionalFormat>();
            return conditions;
        }

        public static void ApplyConditionalFormattingFromColDictionary(ExcelWorksheet worksheet, Dictionary<string, string> conditionalFormattingDictionary, int totalCols, int totalRows, int step)
        {
            var headerRow = 1;
            if (conditionalFormattingDictionary != null && conditionalFormattingDictionary.Count() > 0)
            {
                var columnNameVsAddressDic = new Dictionary<string, string>();
                for (var k = 1; k <= totalCols; k++)
                {
                    var key = worksheet.Cells[headerRow, k].Value?.ToString() ?? "";
                    if (!columnNameVsAddressDic.ContainsKey(key))
                    {
                        columnNameVsAddressDic.Add(key, worksheet.Cells[headerRow + 1, k].Address);
                    }
                }

                for (var i = 1; i <= totalCols; i++)
                {
                    if (conditionalFormattingDictionary.ContainsKey(worksheet.Cells[headerRow, i].Value?.ToString()))
                    {
                        var conditions = ParseConditionalFormatting(conditionalFormattingDictionary[worksheet.Cells[headerRow, i].Value?.ToString()], ",", columnNameVsAddressDic);

                        var rng = worksheet.Cells[headerRow + 1, i, totalRows + headerRow, i];

                        if (step > 1)
                        {
                            rng = worksheet.Cells[headerRow + 2, i, totalRows + headerRow + 1, i];
                        }

                        foreach (var condition in conditions)
                        {
                            var multipleFormula = condition.Formula.Split(':').ToList();
                            var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                            condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.Color);
                            if (multipleFormula.Count > 1)
                            {
                                condFormat.Formula =
                                    $"=AND(({new ExcelFormulaAddress(rng.Address, worksheet)}  {multipleFormula[0]}),({new ExcelFormulaAddress(rng.Address, worksheet)}  {multipleFormula[1]}))";
                            }
                            else
                            {
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address, worksheet) + condition.Formula;
                            }
                        }
                    }
                }
            }
        }

        public static void ApplyConditionalFormattingFromColProperty(ExcelWorksheet worksheet, List<TableFieldAttribute> columnsToColor, int totalCols, int totalRows)
        {
            if (columnsToColor.Count > 0)
            {
                var headerRow = 1;
                var columnNameVsAddressDic = new Dictionary<string, string>();
                for (var k = 1; k <= totalCols; k++)
                {
                    var key = worksheet.Cells[headerRow, k].Value?.ToString() ?? "";
                    if (!columnNameVsAddressDic.ContainsKey(key))
                    {
                        columnNameVsAddressDic.Add(key, worksheet.Cells[headerRow + 1, k].Address);
                    }
                }

                foreach (var colorColumn in columnsToColor)
                {
                    for (var j = 1; j <= totalCols; j++)
                    {
                        if (worksheet.Cells[headerRow, j].Value?.ToString() == colorColumn.ColumnName)
                        {
                            var conditions = ParseConditionalFormatting(colorColumn.ConditionalFormat, colorColumn.ConditionalSeparators, columnNameVsAddressDic);

                            var rng = worksheet.Cells[headerRow + 1, j, totalRows + 1, j];
                            foreach (var condition in conditions)
                            {
                                var condFormat = worksheet.ConditionalFormatting.AddExpression(rng);
                                condFormat.Style.Fill.BackgroundColor.Color = Color.FromName(condition.Color);
                                condFormat.Formula = new ExcelFormulaAddress(rng.Address, worksheet) + condition.Formula;
                            }
                        }
                    }
                }
            }
        }
    }

    public class ExcelConditionalFormat
    {
        public string Condition { get; set; }
        public string Formula { get; set; }
        public string Color { get; set; }
    }
}
