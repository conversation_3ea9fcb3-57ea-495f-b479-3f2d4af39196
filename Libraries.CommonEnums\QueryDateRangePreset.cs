﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum AggregationType
{
    [Display(Name = "Sum")] sum = 0,

    [Display(Name = "Count")] count = 10
}

public enum ChartSize
{
    [Display(Name = "Small")] small = 0,

    [Display(Name = "Medium")] medium = 10,

    [Display(Name = "Large")] large = 20
}

public enum ChartType
{
    [Display(Name = "Bar Chart")] Bar = 10,

    [Display(Name = "Additional Bar Chart")]
    AdditionalBar = 11,

    [Display(Name = "Table")] Table = 20,

    [Display(Name = "Column")] Pie = 30,

    [Display(Name = "Grouped Bar Chart")] Grouped = 40,

    [Display(Name = "FullPageTable")] FullPageTable = 50,

    [Display(Name = "One Number")] OneNumber = 60,

    [Display(Name = "Two Number")] TwoNumber = 70,

    [Display(Name = "KPI Chart")] KPIChart = 80,

    [Display(Name = "Flat Table")] FlatTable = 90,

    [Display(Name = "FullPageTableNLT")] FullPageTableNLT = 100,

    [Display(Name = "FullPageTableLT")] FullPageTableLT = 110,

    [Display(Name = "KPI Chart")] KPIChartNLT = 120,

    [Display(Name = "Grouped Bar Chart")] GroupedNoLimit = 130,
    [Display(Name = "Line Chart")] LineChart = 140
}

public enum ComparisonTimePeriod
{
    Unkown = 0,

    [Display(Name = "Today")] Day = 5,

    [Display(Name = "Current Week")] Week = 10,

    [Display(Name = "MTD")] Month = 15
}

public enum ComparisonType
{
    Unkown = 0,

    [Display(Name = "Yesterday")] Yesterday = 5,

    [Display(Name = "Last Week")] LastWeek = 10,

    [Display(Name = "LMTD")] LastMonth = 15,

    [Display(Name = "Last Month Current Week")]
    LastMonthCurrentWeek = 20,

    [Display(Name = "Last Month Current Day")]
    LastMonthCurrentDay = 25

    //[Display(Name = "This month average")]
    //ThisMonthAverage = 30,
}

public enum DateRangePreset
{
    //[Display(Name = "Custom")]
    //Custom =0,
    [Display(Name = "Today")] Today = 5,

    [Display(Name = "Yesterday")] Yesterday = 10,

    [Display(Name = "Last 7 Days")] Last7Days = 20,

    [Display(Name = "Last 30 Days")] Last30Days = 30,

    [Display(Name = "Last 3 Months")] Last3Months = 40,

    [Display(Name = "MTD")] MTD = 50,

    [Display(Name = "Custom Date")]
    CustomDate =
        60, // Used specifically for MOM User Performance Quick Viz As of 4th march 2022

    [Display(Name = "Custom Date")] Unknown = 0
}

public enum PerspectiveMeasure
{
    [Display(Name = "")]
    Unknown = 0,

    [Display(Name = "CountDistinct")]
    CountDistinct = 1,

    [Display(Name = "Count")]
    Count = 2,

    [Display(Name = "Yes/No")]
    Bool = 3,

    [Display(Name = "Sum")]
    Sum = 4,

    [Display(Name = "Avg.")]
    Avg = 5,

    [Display(Name = "(%)")]
    Percent = 6,

    [Display(Name = "Val")]
    Value = 7,
    [Display(Name = "Min")]
    Min = 8,
    [Display(Name = "Max")]
    Max = 9,
    DerivedMeasure = 10 //temporary for derived measure Total logic set as 'SUM'
}

public enum QueryDateRangePreset
{
    [Display(Name = "Today")] Today = 5,

    [Display(Name = "Yesterday")] Yesterday = 10,

    [Display(Name = "Last 7 Days")] Last7Days = 20,

    [Display(Name = "Last 30 Days")] Last30Days = 30,

    [Display(Name = "Last 3 Months")] Last3Months = 40,

    [Display(Name = "MTD")] MTD = 50
}

public enum SortingType
{
    [Display(Name = "Ascending")] ascending = 0,

    [Display(Name = "Descending")] descending = 10
}

public enum ViewPerspective
{
    Unknown = 0,
    #region GT Perspectives
    [Display(Name = "Product Performance")]
    ProductWiseSales = 1,
    [Display(Name = "Day Summary")]
    DayStart = 2,
    [Display(Name = "Outlet Performance")]
    NoSalesReason = 3,
    [Display(Name = "Live Sales Data")]
    LiveSalesData = 4,
    [Display(Name = "Master Data")]
    MasterData = 5,
    [Display(Name = "Historical Sales Data")]
    HistoricalSalesData = 6,
    #endregion

    #region MT Old Perspectives
    [Display(Name = "Stocks And Sales")]
    MTStockAndSales = 10,
    [Display(Name = "Live Sales Data (MT)")]
    MTLiveSalesData = 11,
    #endregion

    //Add New GT Perspectives here
    #region GT Continues
    [Display(Name = "Master+Visit")]
    MasterVisit = 12,
    [Display(Name = "L3M - User Performance")] //Trend Report Non Linear Time
    TrendReportNLT = 13,
    [Display(Name = "MoM Trend User Performance")]  //Trend Report Linear Timeframe
    TrendReportLT = 14,
    [Display(Name = "User Performance")]
    EmpGeoPerformanceData = 15,
    [Display(Name = "L3M Trend")] //Trend Report Non Linear Time Position
    TrendReportNLTPosition = 16,
    [Display(Name = "Month on Month Trend Report")]  //Trend Report Linear Timeframe Position
    TrendReportLTPosition = 17,
    [Display(Name = "Outlet wise Demand vs Sales (Alpha)")]
    SecondaryDemandVsSales = 18,
    [Display(Name = "Product Demand vs Sales (Alpha)")]
    ProductDemandVsSales = 19,
    [Display(Name = "Distributor Performance (Alpha)")]
    DistributorPerformance = 20,
    [Display(Name = "Distributor Product Performance")]
    DistributorProductPerformance = 21,
    [Display(Name = "Employee Performance")]
    EmployeePerformance = 22,
    [Display(Name = "Van Performance")]
    DailyVanProductPerformance = 24,
    [Display(Name = "Live Sales Data")]
    LiveSalesDataPosition = 25,
    [Display(Name = "Flexible Target vs Achievement")]
    FlexibleTargetVsAchievement = 30,
    [Display(Name = "Distributor Stock Sale")]
    DistributorStockSale = 52,
    [Display(Name = "Primary Orders")]
    PrimaryOrders = 53,
    #endregion

    #region MT new
    [Display(Name = "Out of Stock")]
    MTOutofStock = 50,
    [Display(Name = "Shelf Share")]
    MTShelfShare = 51,
    [Display(Name = "MT Supervisor Stock And Sales")]
    MTMerchandiserStockAndSale = 55,
    #endregion
}
public enum NLTFrames
{
    [Display(Name = "MTD")] MTD = 1,

    [Display(Name = "LMTD")] LMTD = 2,

    [Display(Name = "LM")] LM = 3,

    [Display(Name = "L3M Avg")] L3MAvg = 4,

    [Display(Name = "%L3M")] PerL3M = 5,

    [Display(Name = "% Growth (MTD vs LMTD)")]
    PerGrowth = 10,
}

public enum LTFrames
{
    [Display(Name = "Weekly")] Weekly = 1,

    [Display(Name = "Monthly")] Monthly = 2,

    [Display(Name = "Quarterly")] Quarterly = 3
}

public enum NameCategory
{
    [Display(Name = "Position Name")] PositionName = 1,

    [Display(Name = "User Name")] UserName = 2
}

public enum PivotCategory
{
    Unknown = 0,

    [Display(Name = "Time")] Time = 1,

    [Display(Name = "Category")] Category = 2
}

public enum PerspectiveType
{
    Attendance,
    DayStart,
    Sales
}
