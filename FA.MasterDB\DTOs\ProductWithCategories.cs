﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.Core.DTOs
{
    public class ProductWithCategories
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public string StdUnit { get; set; }
        public decimal? UnitWeight { get; set; }
        public decimal? NetWeight { get; set; }
        public decimal? GrossWeight { get; set; }
        public decimal MRPNumeric { get; set; }
        public double Price { get; set; }
        public long? SecondaryCategoryId { get; set; }
        public string SecondaryCategory { get; set; }
        public long? PrimaryCategoryId { get; set; }
        public string PrimaryCategory { get; set; }
        public string VariantName { get; set; }
        public long? ProductDivisionId { get; set; }
        public string ProductDivision { get; set; }
        public int? HSNCode { get; set; }
        public string ErpCode { get; set; }
        public bool IsSaleable { get; set; }
        public double StandardUnitConversionFactor { get; set; }
        public double SuperUnitConversionFactor { get; set; }
        public long? ProductDisplayCategoryId { get; set; }
        public string? Product_AttributeText1 { get; set; }
        public string Category1 { get; set; }
        public string? DisplayCategory { get; set; }
        public string Description { get; set; }
        public string Color { get; set; }
        public string? Product_AttributeText2 { get; set; }
        public long? ProductGroupId { get; set; }
        public double? AdditionalUnitConversionFactor { get; set; }
        public string? AdditionalUnit { get; set; }
    }
}
