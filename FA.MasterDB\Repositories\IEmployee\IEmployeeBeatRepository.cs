﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.Core.Repositories.IEmployee
{

    public interface IEmployeeBeatRepository
    {
        Task<string> GetBeat(long companyId, long employeeId);
        Task<long> GetEmployeeByBeatId(long companyId, long beatId);
        Task<long> GetEmployeeByBeatIds(long companyId, List<long> beatIds);
        Task<long> GetEmployeeByProductDivisionIds(long companyId, List<long> beatIds, long productDivisionId);
    }
}
