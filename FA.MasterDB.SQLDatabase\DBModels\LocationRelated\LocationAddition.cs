﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{

    public class LocationAddition
    {
        public GeoLocation GeoLocation { get; set; }

        public string ShopName { get; set; }

        [StringLength(500, ErrorMessage = "Address cannot exceed 500 characters")]
        public string Address { get; set; }

        [StringLength(50, ErrorMessage = "Owners Name cannot exceed 50 characters")]
        public string OwnersName { get; set; }

        public string OwnersNo { get; set; }

        public string MarketName { get; set; }

        public string CityName { get; set; }

        public string PlaceOfDelivery { get; set; }

        public string StateName { get; set; }

        [StringLength(50)]
        public string TIN { get; set; }

        public string GSTIN { get; set; }

        // [StringLength(6, MinimumLength = 5), DefaultValue("000000")]
        public string PinCode { get; set; }

        public string Email { get; set; }

        public long BeatId { get; set; }

        public string Id { get; set; }

        public long Time { get; set; }

        public string Image { get; set; }

        public string ShopType { get; set; }

        public OutletSegmentation Segmentation { get; set; }

        public OutletChannel? OutletChannel { get; set; }

        public string SubCity { get; set; }

        public string FormattedAddress { get; set; }

        public long? ShopTypeId { get; set; }

        public string Captured_Address { get; set; }

        public string Captured_MarketName { get; set; }

        public string District { get; set; }

        public string Country { get; set; }

        public List<long> RouteIds { get; set; }

        public string AttributeText1 { get; set; }

        public string AttributeText2 { get; set; }

        public string AttributeText3 { get; set; }

        public string AttributeText4 { get; set; }

        public double? AttributeNumber1 { get; set; }

        public double? AttributeNumber2 { get; set; }

        public double? AttributeNumber3 { get; set; }

        public double? AttributeNumber4 { get; set; }

        public bool? AttributeBoolean1 { get; set; }

        public bool? AttributeBoolean2 { get; set; }

        public string AttributeDate1 { get; set; }

        public string AttributeDate2 { get; set; }

        public string AttributeImage1 { get; set; }

        public string AttributeImage2 { get; set; }

        public string AttributeImage3 { get; set; }

        public string PAN { get; set; }

        public long? GeographicalMappingId { get; set; }

        public long? PositionCodeId { get; set; }
    }

    public class GeoLocation
    {
        public string Captured_Address { get; set; }

        public bool HasValue => Longitude.HasValue && Latitude.HasValue;

        public bool IsGPSOff { get; set; }
        public decimal? Latitude { get; set; }
        public double? LocationAccuracy { get; set; }
        public decimal? Longitude { get; set; }
        public string Source { get; set; }
    }
}
