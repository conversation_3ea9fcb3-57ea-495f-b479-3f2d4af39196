﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class DistributorProductDivisionMappingDB
    {
        public long Id { get; set; }

        public long DistributorId { get; set; }

        public long ProductDivisionId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        [ForeignKey("Company")]
        public long CompanyId { get; set; }

        public bool Deleted { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
