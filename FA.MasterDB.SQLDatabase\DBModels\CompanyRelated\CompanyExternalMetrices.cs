﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    [Table("CompanyExternalMetrices")]
    public class CompanyExternalMetrices
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("Name")]
        public string Name { get; set; }

        [Column("ErpId")]
        public string ErpId { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("IsDeleted")]
        public bool IsDeleted { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("DataType")]
        public OutletMetricDataType DataType { get; set; }

        [Column("Perspective")]
        public CueCardPerspective Perspective { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("Description")]
        public string Description { get; set; }

        [Column("Metricfields")]
        public string Metricfields { get; set; }
    }
}
