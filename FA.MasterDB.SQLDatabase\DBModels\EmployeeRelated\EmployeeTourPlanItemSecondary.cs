﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

public class EmployeeTourPlanItemSecondary
{
    public long Id { get; set; }

    public long EmployeeTourPlanItemId { get; set; }

    public DateTime ItemDate { get; set; }

    public long? BeatId { get; set; }

    public long? RouteId { get; set; }

    public string ReasonCategory { get; set; }

    public string Reason { get; set; }

    public long? JWFieldUserId { get; set; }

    public long CompanyId { get; set; }

    public int Sequence { get; set; }

    public virtual ICollection<EmployeeTourPlanItemSecondary> EmployeeTourPlanItemsSecondary { get; set; }
}
