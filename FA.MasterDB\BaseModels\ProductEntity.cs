﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.Core.BaseModels
{
    public class ProductEntity
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string VariantName { get; set; }
        public bool IsActive { get; set; }
        public bool Deleted { get; set; }
        public long CompanyId { get; set; }
        public string Unit { get; set; }

        // Weight Properties
        public decimal? UnitWeight { get; set; }
        public decimal? NetWeight { get; set; }
        public decimal? GrossWeight { get; set; }

        public bool IsSaleable { get; set; }
        public double StandardUnitConversionFactor { get; set; }
        public double SuperUnitConversionFactor { get; set; }
        public string ErpCode { get; set; }

        // Category Information
        public long ProductCategoryId { get; set; }
        public string Category1 { get; set; }
        public long? ProductDisplayCategoryId { get; set; }

        // Pricing and Promotion
        public decimal Price { get; set; }
        public string MRP { get; set; }
        public decimal MRPNumeric { get; set; }
        public bool IsPromoted { get; set; }

        // Additional Information
        public string ColorName { get; set; }
        public int? DisplayOrder { get; set; }
        public int? ExpiryInDays { get; set; }
        public int MBQ { get; set; }

        // HSN Code
        public int? HSNCode { get; set; }

        // Other properties (add as needed)

        // Constructor
        public ProductEntity()
        {
            // Set default values or perform any initialization if needed
        }
    }
}
