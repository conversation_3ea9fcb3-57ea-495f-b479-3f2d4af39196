﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes
{
    public class QuantityPurchaseScheme
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public long LocationId { get; set; }

        public LocationDB Location { get; set; }

        public string SchemeERPId { get; set; }

        public DateTime SchemeStartDate { get; set; }

        public DateTime SchemeEndDate { get; set; }

        public bool IsActive { get; set; }

        public string Data { get; set; }

        public DateTime StatusDate { get; set; }
    }
}
