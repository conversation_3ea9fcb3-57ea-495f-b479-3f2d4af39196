﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories;
using FA.MasterDB.Core.Repositories.IDistributor;
using FA.MasterDB.Core.Repositories.IEmployee;
using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.Core.Repositories.IProduct;
using FA.MasterDB.Core.Repositories.ITertiary;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository.DistributorRepo;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository.EmployeeRepo;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository.ProductRepo;
using FA.MasterDB.SQLDatabase.EntityFrameworkRepository.TertiaryRepo;
using FA.MasterDB.SQLDatabase.SQLQueryRepository;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FA.MasterDB.SQLDatabase.Dependency
{
    public class ConfigureDependency
    {
        private static string GetConnectionString(IConfiguration configuration, string key)
        {
            return configuration.GetConnectionString(key) ?? throw new NullReferenceException();
        }

        private static void SqlResiliencyBuilder(SqlServerDbContextOptionsBuilder o)
        {
            o.EnableRetryOnFailure();
        }

        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            var readOnlyMasterDbConnectionString = GetConnectionString(configuration, "ReadOnlyMasterDbConnectionString");

            // SFA master database
            serviceProvider.AddDbContext<MasterDbContext>(options =>
                options.UseSqlServer(readOnlyMasterDbConnectionString, SqlResiliencyBuilder));
            serviceProvider.AddTransient(e => new MasterDbDataReader(readOnlyMasterDbConnectionString));

            // Entity Framework repo Registration
            RegisterProductRepositories(serviceProvider);
            RegisterDistributorRepositories(serviceProvider);
            RegisterLocationRepositories(serviceProvider);
            RegisterAddressRepositories(serviceProvider);
            RegisterBeatRepositories(serviceProvider);
            RegisterEmployeeRepositories(serviceProvider);
            RegisterOtherRepositories(serviceProvider);
            RegisterTertiaryRepositories(serviceProvider);

            // Additional services can be registered here if needed
        }

        private static void RegisterProductRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IProductRepository, ProductDataAccessRepository>();
            serviceProvider.AddTransient<IProductCategoryRepository, ProductDataAccessRepository>();
            serviceProvider.AddTransient<IProductTaxationRepository, ProductSQLDataReader>();
            serviceProvider.AddTransient<IProductDisplayCategoriesRepository, ProductDisplayCategoriesRepository>();
        }

        private static void RegisterDistributorRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IDistributorRepository, DistributorRepository>();
        }

        private static void RegisterLocationRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<ILocationRepository, LocationRepository>();
            serviceProvider.AddTransient<IShopTypeRepository, ShopTypeRepository>();
            serviceProvider.AddTransient<IOutletSegmentationAttributesRepository,
                OutletSegmentationAttributesRepository>();
            serviceProvider.AddTransient<IChannelRepository, ChannelRepository>();
        }

        private static void RegisterAddressRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IBilledToShippedToAddressRepository, BilledToShippedToAddressRepository>();
            serviceProvider.AddTransient<IDistributorAddressRepository, DistributorAddressRepository>();
        }

        private static void RegisterBeatRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IDistributorBeatMappingRepository, DistributorBeatMappingRepository>();
            serviceProvider.AddTransient<IEmployeeBeatRepository, EmployeeBeatRepository>();
        }

        private static void RegisterEmployeeRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IEmployeeRepository, EmployeeRepository>();
        }

        private static void RegisterOtherRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<IRoutesRepository, RoutesRepository>();
            serviceProvider.AddTransient<ISchemeRepository, SchemeRepository>();
            serviceProvider.AddTransient<ISchemeSlabRepository, SchemeSlabRepository>();
            serviceProvider.AddTransient<IVanMasterRepository, VanMasterRepository>();
            serviceProvider.AddTransient<ISchemeSQLRepository, SchemeSQLRepository>();
        }

        private static void RegisterTertiaryRepositories(IServiceCollection serviceProvider)
        {
            serviceProvider.AddTransient<ITertiaryRepository, TertiaryDataAccessRepository>();
        }
    }
}
