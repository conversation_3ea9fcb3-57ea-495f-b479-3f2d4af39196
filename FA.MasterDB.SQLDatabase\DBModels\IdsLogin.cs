﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("IDSLogins")]
    public class IdsLogin
    {
        public long Id { get; set; }

        public string EmailId { get; set; }

        public Guid LoginGuid { get; set; }

        public long LocalId { get; set; }

        public PortalUserRole UserRole { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public string PhoneNo { get; set; }

        public string Name { get; set; }

        public long CompanyId { get; set; }

        public bool IsManager =>
            UserRole == PortalUserRole.AreaSalesManager ||
            UserRole == PortalUserRole.RegionalSalesManager ||
            UserRole == PortalUserRole.ZonalSalesManager ||
            UserRole == PortalUserRole.NationalSalesManager ||
            UserRole == PortalUserRole.GlobalSalesManager;

        public virtual Company Company { get; set; }
    }
}
