﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.Route;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    namespace FieldAssist.DataAccessLayer.Models.EFModels
    {
        public class DistributorRouteMapping
        {
            public long Id { get; set; }

            public long DistributorId { get; set; }

            [ForeignKey("Route")]
            public long RouteId { get; set; }

            public DateTime CreatedAt { get; set; }

            public string CreationContext { get; set; }

            [ForeignKey("Company")]
            public long CompanyId { get; set; }

            public bool Deleted { get; set; }

            public virtual Company Company { get; set; }

            public virtual Distributor FADistributor { get; set; }

            public virtual RouteClass Route { get; set; }
        }
    }
}
