﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Position
{
    [Table("PositionProductDivisionMappings")]
    public class PositionProductDivisionMapping
    {
        public PositionProductDivisionMapping()
        {
            IsDeactive = false;
        }

        [Column("Id")]
        public long Id { get; set; }

        [Column("PositionId")]
        public long PositionId { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [Column("ProductDivisionId")]
        public long ProductDivisionId { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }
    }
}
