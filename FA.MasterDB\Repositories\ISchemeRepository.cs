﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) Microsoft.  All Rights Reserved.  Licensed under the MIT license.  See License.txt in the project root for license information.

using FA.MasterDB.Core.BaseModels;

namespace FA.MasterDB.Core.Repositories
{

    public interface ISchemeRepository
    {
        Task<List<SchemeEntity>> GetSchemes(long companyId);
        Task<List<SchemeEntity>> GetSchemesWithSlab
            (long companyId);
    }
}
