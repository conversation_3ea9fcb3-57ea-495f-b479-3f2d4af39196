﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("FAProductSecondaryCategory")]
    public class ProductSecondaryCategory
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public long CompanyId { get; set; }
        public long ProductPrimaryCategoryId { get; set; }
        public ProductPrimaryCategory ProductPrimaryCategory { get; set; }
        public int OrderPosition { get; set; }
    }
}
