﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("ExternalApiTokens")]
    public class DbExtApiToken
    {
        public long Id { get; set; }
        public string TokenUserName { get; set; }
        public string TokenPassword { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
    }
}
