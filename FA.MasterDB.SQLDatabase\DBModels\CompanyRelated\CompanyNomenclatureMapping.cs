﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    public class CompanyNomenclatureMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        [ForeignKey("CompanyNomenclature")]
        public int NomenclatureId { get; set; }

        public string Name { get; set; }

        public string HelpText { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public CompanyNomenclature CompanyNomenclature { get; set; }
    }
}
