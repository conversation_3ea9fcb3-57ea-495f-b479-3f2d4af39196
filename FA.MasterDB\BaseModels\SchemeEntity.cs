﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.Core.BaseModels
{
    public class SchemeEntity
    {
        // Properties related to scheme details
        public long Id { get; set; }
        public Guid Guid { get; set; }
        public string Name { get; set; }
        public string StringDescription { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public decimal DiscountValue { get; set; }
        public DiscountBlock DiscountOn { get; set; }
        public string DiscountBlockArray { get; set; }
        public ConstraintType ConstraintType { get; set; }
        public string OutletConstraints { get; set; }
        public Enum.SchemeCategorization Category { get; set; }
        public PayoutType PayoutType { get; set; }
        public bool IsNew { get; set; }
        public PayoutCalculationType PayoutCalculationType { get; set; }
        public DiscountBlock? PayoutOn { get; set; }
        public string SelectedProductBlockArray { get; set; }
        public SchemeType? SchemeType { get; set; }
        public SchemeSubType? SchemeSubType { get; set; }
        public SchemeSubType? SchemeSubType2 { get; set; }

        // Properties related to status and metadata
        public bool IsActive { get; set; }
        public bool Deleted { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }

        // Properties related to foreign keys
        public long CompanyId { get; set; }
        public string DistributorIdBlock { get; set; }
        public long? ZoneId { get; set; }
        public long? RegionId { get; set; }
        public string ErpId { get; set; }
        public ICollection<SchemeSlabBase> SchemeSlabs { get; set; }

        // Constructor to set default values
        public SchemeEntity()
        {
            IsActive = true;
            Deleted = false;
            IsNew = true;
        }
    }
}
