﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Journey
{
    public class JourneyWeek
    {
        public long Id { get; set; }

        public JourneyCycle JourneyCycle { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long JourneyCycleId { get; set; }

        public int WeekForMonth { get; set; }

        public int QuarterNumber { get; set; }

        public int WeekForQuarter { get; set; }

        public int WeekForYear { get; set; }

        public DateTime WeekStartDate { get; set; }

        public DateTime WeekEndDate { get; set; }

        public DateTime CreatedAt { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }
    }
}
