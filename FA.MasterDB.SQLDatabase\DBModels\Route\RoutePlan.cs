﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Route
{
    public class RoutePlan
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public DateTime EffectiveDate { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public long EmployeeId { get; set; }

        public bool IsDeactive { get; set; }

        public bool Deleted { get; set; }

        public ICollection<RoutePlanItem> RoutePlanItems { get; set; }

        public ICollection<SecondaryRoutePlanItem> SecondaryRoutePlanItems { get; set; }

        public JourneyFrequency RepeatFrequency { get; set; }
    }
}
