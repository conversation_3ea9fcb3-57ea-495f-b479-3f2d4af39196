﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Gamification;

[Table("TargetForTeams")]
public class TargetForTeams
{
    public long Id { get; set; }

    public long GameId { get; set; }

    public long TeamId { get; set; }

    public long KpiId { get; set; }

    public string Target { get; set; }

    public bool IsQualifier { get; set; }

    public bool IsActive { get; set; }

    public Game Game { get; set; }

    public ICollection<KPISlab> KPISlabs { get; set; }

    public bool IsContinuous { get; set; }
}
