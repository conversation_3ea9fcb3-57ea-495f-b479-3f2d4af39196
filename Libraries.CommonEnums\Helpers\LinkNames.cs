﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;

namespace Libraries.CommonEnums.Helpers;

public class LinkNames
{
    public string AreaSalesManager { set; get; }
    public string RegionalSalesManager { set; get; }
    public string Employee { get; set; }
    public string Distributor { set; get; }
    public string Beats { get; set; }
    public string Outlets { get; set; }
    public string Sales { get; set; }
    public string ActualSales { get; set; }
    public string Targets { get; set; }
    public string Attendance { get; set; }
    public string Supplies { get; set; }
    public string Beat { get; set; }
    public string ZonalSalesManager { set; get; }
    public string NationalSalesManager { set; get; }
    public string GlobalSalesManager { set; get; }
    public string PrimaryCategory { get; set; }
    public string SecondaryCategory { get; set; }
    public string AlternateCategory { get; set; }
    public string EmployeeAreaName { get; set; }
    public string Zone { get; set; }
    public string Designation { get; set; }
    public string Region { get; set; }
    public string Territory { get; set; }
    public string PromotedProducts { get; set; }
    public string MustSell { get; set; }
    public string Stockist { get; set; }
    public string SubStockist { get; set; }
    public string SuperStockist { get; set; }
    public string UTC { get; set; }
    public string UPC { get; set; }
    public string LPC { get; set; }
    public string EffectiveCall { get; set; }
    public string Focused { get; set; }
    public string FocusedOutlet { get; set; }
    public string Unit { get; set; }
    public string StdUnit { get; set; }
    public string SuperUnit { get; set; }

    public string AvgValuePerCall { get; set; }
    public string SuggestedQty { get; set; }
    public string ISRSummary { get; set; }
    public string TertiarySales { get; set; }
    public string StockInward { get; set; }
    public string Teams { get; set; }
    public string Level5 { get; set; }
    public string Level6 { get; set; }
    public string Level7 { get; set; }
    public string GSMPosition { get; set; }
    public string NSMPosition { get; set; }
    public string ZSMPosition { get; set; }
    public string RSMPosition { get; set; }
    public string ASMPosition { get; set; }
    public string SEPosition { get; set; }
    public string AttributeDate1 { get; set; }
    public string AttributeDate2 { get; set; }
    public string AttributeImage1 { get; set; }
    public string AttributeImage2 { get; set; }
    public string AttributeImage3 { get; set; }
    public string AttributeBoolean1 { get; set; }
    public string AttributeBoolean2 { get; set; }
    public string AttributeNumber1 { get; set; }
    public string AttributeNumber2 { get; set; }
    public string AttributeNumber3 { get; set; }
    public string AttributeNumber4 { get; set; }
    public string AttributeText1 { get; set; }
    public string AttributeText2 { get; set; }
    public string AttributeText3 { get; set; }
    public string AttributeText4 { get; set; }

    public string ProductDivision { get; set; }
    public string L8Position { get; set; }
    public string L7Position { get; set; }
    public string L6Position { get; set; }
    public string L5Position { get; set; }
    public string L4Position { get; set; }
    public string L3Position { get; set; }
    public string L2Position { get; set; }
    public string L1Position { get; set; }
    public string CompanyAdmin { get; set; }
    public string OVT { get; set; }
    public string Retailing { get; set; }
    public string CAP { get; set; }
    public string CampaignName { get; set; }
    public string Route { get; set; }

    public static LinkNames GetLinkNames(Dictionary<string, string> nomenclatureDict)
    {
        return new LinkNames
        {
            Sales = nomenclatureDict.ContainsKey("Sales") ? nomenclatureDict["Sales"] : "Sales",
            ActualSales = nomenclatureDict.ContainsKey("ActualSales") ? nomenclatureDict["ActualSales"] : "Actual Sales",
            Targets = nomenclatureDict.ContainsKey("Targets") ? nomenclatureDict["Targets"] : "Targets",
            Outlets = nomenclatureDict.ContainsKey("Outlets") ? nomenclatureDict["Outlets"] : "Outlets",
            Employee = nomenclatureDict.ContainsKey("Employee") ? nomenclatureDict["Employee"] : "Employee",
            Attendance = nomenclatureDict.ContainsKey("Attendance") ? nomenclatureDict["Attendance"] : "Attendance",
            Supplies = nomenclatureDict.ContainsKey("Supplies") ? nomenclatureDict["Supplies"] : "Supplies",
            Beats = nomenclatureDict.ContainsKey("Beats") ? nomenclatureDict["Beats"] : "Beats",
            Beat = nomenclatureDict.ContainsKey("Beat") ? nomenclatureDict["Beat"] : "Beat",
            AreaSalesManager = nomenclatureDict.ContainsKey("AreaSalesManager") ? nomenclatureDict["AreaSalesManager"] : "ASM",
            RegionalSalesManager = nomenclatureDict.ContainsKey("RegionalSalesManager") ? nomenclatureDict["RegionalSalesManager"] : "RSM",
            Distributor = nomenclatureDict.ContainsKey("Distributor") ? nomenclatureDict["Distributor"] : "Distributor",
            ZonalSalesManager = nomenclatureDict.ContainsKey("ZonalSalesManager") ? nomenclatureDict["ZonalSalesManager"] : "ZSM",
            NationalSalesManager = nomenclatureDict.ContainsKey("NationalSalesManager") ? nomenclatureDict["NationalSalesManager"] : "NSM",
            GlobalSalesManager = nomenclatureDict.ContainsKey("GlobalSalesManager") ? nomenclatureDict["GlobalSalesManager"] : "GSM",
            PrimaryCategory = nomenclatureDict.ContainsKey("PrimaryCategory") ? nomenclatureDict["PrimaryCategory"] : "Primary Category",
            SecondaryCategory = nomenclatureDict.ContainsKey("SecondaryCategory") ? nomenclatureDict["SecondaryCategory"] : "Secondary Category",
            AlternateCategory = nomenclatureDict.ContainsKey("AlternateCategory") ? nomenclatureDict["AlternateCategory"] : "Alternate Category",
            EmployeeAreaName = nomenclatureDict.ContainsKey("EmployeeAreaName") ? nomenclatureDict["EmployeeAreaName"] : "Employee Area",
            Zone = nomenclatureDict.ContainsKey("Zone") ? nomenclatureDict["Zone"] : "Zone",
            Region = nomenclatureDict.ContainsKey("Region") ? nomenclatureDict["Region"] : "Region",
            Territory = nomenclatureDict.ContainsKey("Territory") ? nomenclatureDict["Territory"] : "Territory",
            PromotedProducts = nomenclatureDict.ContainsKey("PromotedProducts") ? nomenclatureDict["PromotedProducts"] : "Promoted Product",
            MustSell = nomenclatureDict.ContainsKey("MustSell") ? nomenclatureDict["MustSell"] : "Must Sell",
            Stockist = nomenclatureDict.ContainsKey("Stockist") ? nomenclatureDict["Stockist"] : "Stockist",
            SubStockist = nomenclatureDict.ContainsKey("SubStockist") ? nomenclatureDict["SubStockist"] : "Sub Stockist",
            SuperStockist = nomenclatureDict.ContainsKey("SuperStockist") ? nomenclatureDict["SuperStockist"] : "Super Stockist",
            UTC = nomenclatureDict.ContainsKey("UTC") ? nomenclatureDict["UTC"] : "UTC",
            UPC = nomenclatureDict.ContainsKey("UPC") ? nomenclatureDict["UPC"] : "UPC",
            LPC = nomenclatureDict.ContainsKey("LPC") ? nomenclatureDict["LPC"] : "LPC",
            EffectiveCall = nomenclatureDict.ContainsKey("EffectiveCall") ? nomenclatureDict["EffectiveCall"] : "Effective Call",
            Focused = nomenclatureDict.ContainsKey("Focused") ? nomenclatureDict["Focused"] : "Focused",
            FocusedOutlet = nomenclatureDict.ContainsKey("FocusedOutlet") ? nomenclatureDict["FocusedOutlet"] : "Focused Outlet",
            Unit = nomenclatureDict.ContainsKey("Unit") ? nomenclatureDict["Unit"] : "Unit",
            SuperUnit = nomenclatureDict.ContainsKey("SuperUnit") ? nomenclatureDict["SuperUnit"] : "Super Unit",
            StdUnit = nomenclatureDict.ContainsKey("StdUnit") ? nomenclatureDict["StdUnit"] : "Std Unit",
            AvgValuePerCall = nomenclatureDict.ContainsKey("AvgValuePerCall") ? nomenclatureDict["AvgValuePerCall"] : "Avg. Value Per Call",
            SuggestedQty = nomenclatureDict.ContainsKey("SuggestedQty") ? nomenclatureDict["SuggestedQty"] : "Suggested Qty",
            ISRSummary = nomenclatureDict.ContainsKey("ISRSummary") ? nomenclatureDict["ISRSummary"] : "ISR Summary",
            TertiarySales = nomenclatureDict.ContainsKey("TertiarySales") ? nomenclatureDict["TertiarySales"] : "Tertiary Sales",
            StockInward = nomenclatureDict.ContainsKey("StockInward") ? nomenclatureDict["StockInward"] : "Stock Inward",
            Teams = nomenclatureDict.ContainsKey("Teams") ? nomenclatureDict["Teams"] : "Teams",
            Level5 = nomenclatureDict.ContainsKey("Level5") ? nomenclatureDict["Level5"] : "Level 5",
            Level6 = nomenclatureDict.ContainsKey("Level6") ? nomenclatureDict["Level6"] : "Level 6",
            Level7 = nomenclatureDict.ContainsKey("Level7") ? nomenclatureDict["Level7"] : "Level 7",
            GSMPosition = nomenclatureDict.ContainsKey("GlobalSalesManager") ? nomenclatureDict["GlobalSalesManager"] + " Position" : "GSM Position",
            ASMPosition = nomenclatureDict.ContainsKey("AreaSalesManager") ? nomenclatureDict["AreaSalesManager"] + " Position" : "ASM Position",
            NSMPosition = nomenclatureDict.ContainsKey("NationalSalesManager") ? nomenclatureDict["NationalSalesManager"] + " Position" : "NSM Position",
            RSMPosition = nomenclatureDict.ContainsKey("RegionalSalesManager") ? nomenclatureDict["RegionalSalesManager"] + " Position" : "RSM Position",
            ZSMPosition = nomenclatureDict.ContainsKey("ZonalSalesManager") ? nomenclatureDict["ZonalSalesManager"] + " Position" : "ZSM Position",
            SEPosition = nomenclatureDict.ContainsKey("Employee") ? nomenclatureDict["Employee"] + " Position" : "SE Position",
            AttributeDate1 = nomenclatureDict.ContainsKey("AttributeDate1") ? nomenclatureDict["AttributeDate1"] : "AttributeDate1",
            AttributeDate2 = nomenclatureDict.ContainsKey("AttributeDate2") ? nomenclatureDict["AttributeDate2"] : "AttributeDate2",
            AttributeImage1 = nomenclatureDict.ContainsKey("AttributeImage1") ? nomenclatureDict["AttributeImage1"] : "AttributeImage1",
            AttributeImage2 = nomenclatureDict.ContainsKey("AttributeImage2") ? nomenclatureDict["AttributeImage2"] : "AttributeImage2",
            AttributeImage3 = nomenclatureDict.ContainsKey("AttributeImage3") ? nomenclatureDict["AttributeImage3"] : "AttributeImage3",
            AttributeBoolean1 = nomenclatureDict.ContainsKey("AttributeBoolean1") ? nomenclatureDict["AttributeBoolean1"] : "AttributeBoolean1",
            AttributeBoolean2 = nomenclatureDict.ContainsKey("AttributeBoolean2") ? nomenclatureDict["AttributeBoolean2"] : "AttributeBoolean2",
            AttributeNumber1 = nomenclatureDict.ContainsKey("AttributeNumber1") ? nomenclatureDict["AttributeNumber1"] : "AttributeNumber1",
            AttributeNumber2 = nomenclatureDict.ContainsKey("AttributeNumber2") ? nomenclatureDict["AttributeNumber2"] : "AttributeNumber2",
            AttributeNumber3 = nomenclatureDict.ContainsKey("AttributeNumber3") ? nomenclatureDict["AttributeNumber3"] : "AttributeNumber3",
            AttributeNumber4 = nomenclatureDict.ContainsKey("AttributeNumber4") ? nomenclatureDict["AttributeNumber4"] : "AttributeNumber4",
            AttributeText1 = nomenclatureDict.ContainsKey("AttributeText1") ? nomenclatureDict["AttributeText1"] : "AttributeText1",
            AttributeText2 = nomenclatureDict.ContainsKey("AttributeText2") ? nomenclatureDict["AttributeText2"] : "AttributeText2",
            AttributeText3 = nomenclatureDict.ContainsKey("AttributeText3") ? nomenclatureDict["AttributeText3"] : "AttributeText3",
            AttributeText4 = nomenclatureDict.ContainsKey("AttributeText4") ? nomenclatureDict["AttributeText4"] : "AttributeText4",
            ProductDivision = nomenclatureDict.ContainsKey("ProductDivision") ? nomenclatureDict["ProductDivision"] : "Product Division",
            L8Position = nomenclatureDict.ContainsKey("L8Position") ? nomenclatureDict["L8Position"] : "Level 8 Position",
            L7Position = nomenclatureDict.ContainsKey("L7Position") ? nomenclatureDict["L7Position"] : "Level 7 Position",
            L6Position = nomenclatureDict.ContainsKey("L6Position") ? nomenclatureDict["L6Position"] : "Level 6 Position",
            L5Position = nomenclatureDict.ContainsKey("L5Position") ? nomenclatureDict["L5Position"] : "Level 5 Position",
            L4Position = nomenclatureDict.ContainsKey("L4Position") ? nomenclatureDict["L4Position"] : "Level 4 Position",
            L3Position = nomenclatureDict.ContainsKey("L3Position") ? nomenclatureDict["L3Position"] : "Level 3 Position",
            L2Position = nomenclatureDict.ContainsKey("L2Position") ? nomenclatureDict["L2Position"] : "Level 2 Position",
            L1Position = nomenclatureDict.ContainsKey("L1Position") ? nomenclatureDict["L1Position"] : "Level 1 Position",
            CompanyAdmin = nomenclatureDict.ContainsKey("CompanyAdmin") ? nomenclatureDict["CompanyAdmin"] : "Company Admin",
            OVT = nomenclatureDict.ContainsKey("OVT") ? nomenclatureDict["OVT"] : "OVT",
            Retailing = nomenclatureDict.ContainsKey("Retailing") ? nomenclatureDict["Retailing"] : "Retailing",
            CAP = nomenclatureDict.ContainsKey("CAP") ? nomenclatureDict["CAP"] : "CAP",
            Route = nomenclatureDict.ContainsKey("Route") ? nomenclatureDict["Route"] : "Route"
        };
    }
}
