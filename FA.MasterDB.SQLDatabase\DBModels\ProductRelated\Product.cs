﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("FACompanyProducts")]
    public class ProductTable
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string? VariantName { get; set; }
        public bool IsActive { get; set; }
        public bool Deleted { get; set; }
        public long CompanyId { get; set; }
        public string? Unit { get; set; }
        public decimal? UnitWeight { get; set; }
        public decimal? NetWeight { get; set; }
        public decimal? GrossWeight { get; set; }
        public bool IsSaleable { get; set; }
        public double StandardUnitConversionFactor { get; set; }
        public double SuperUnitConversionFactor { get; set; }
        public string? ErpCode { get; set; }
        public string? Category1 { get; set; }
        public long? ProductDisplayCategoryId { get; set; }
        public double Price { get; set; }
        public ProductSecondaryCategory? ProductCategory { get; set; }
        public string DisplayMRP { get; set; }
        public string? ColorName { get; set; }
        public int? DisplayOrder { get; set; }
        public int? ExpiryInDays { get; set; }
        public int? MBQ { get; set; }
        public string? Schemes { get; set; }
        public bool? IsPromoted { get; set; }
        public decimal MRPNumeric { get; set; }
        public decimal? PTD { get; set; }
        public int? HSNCode { get; set; }
        public string? Product_AttributeText1 { get; set; }
        public virtual ProductDisplayCategories? ProductDisplayCategory { get; set; }

        // Additional properties added from Product
        public string? Description { get; set; }
        public bool IsTopSelling { get; set; }
        public string? LocalName { get; set; }
        public bool IsFocused { get; set; }
        public decimal? PricePlacement { get; set; }
        public decimal? PriceRegular { get; set; }
        public decimal? PriceSuperCash { get; set; }
        public bool IsAssorted { get; set; }
        public decimal? PTDSuper { get; set; }
        public decimal? PTDSub { get; set; }
        //public decimal? PTR_MT { get; set; }
        public long? ProductCESSCategoryId { get; set; }
        public double? AdditionalUnitConversionFactor { get; set; }
        public string? AdditionalUnit { get; set; }
        public string? PackSize { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public double? ProductWeightinGm { get; set; }
        public string? Category2 { get; set; }
        public string? PackagingType { get; set; }
        public int? MOQ { get; set; }
        public string? CategoryImage { get; set; }
        public string? ProductThumbnail { get; set; }

        // Additional attributes as per your initial Product model
        [Column("Product_AttributeText2")]
        public string? Product_AttributeText2 { get; set; }

        [Column("Product_AttributeBoolean1")]
        public bool? Product_AttributeBoolean1 { get; set; }

        [Column("Product_AttributeBoolean2")]
        public bool? Product_AttributeBoolean2 { get; set; }

        [Column("Product_AttributeNumber1")]
        public double? Product_AttributeNumber1 { get; set; }

        [Column("Product_AttributeNumber2")]
        public double? Product_AttributeNumber2 { get; set; }

        [Column("Product_AttributeDate1")]
        public DateTime? Product_AttributeDate1 { get; set; }

        [Column("Product_AttributeDate2")]
        public DateTime? Product_AttributeDate2 { get; set; }
        public long ProductCategoryId { get; set; }

        [Column("ProductGroupID")]
        public long? ProductGroupId { get; set; }
    }
}
