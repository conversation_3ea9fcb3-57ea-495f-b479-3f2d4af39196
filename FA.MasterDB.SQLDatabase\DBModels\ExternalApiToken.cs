﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class ExternalApiToken
    {
        public bool IsDeactive { get; set; }

        public long Id { get; set; }

        public string TokenOwner { get; set; }

        public string TokenUserName { get; set; }

        public string TokenPassword { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public virtual Company Company { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
