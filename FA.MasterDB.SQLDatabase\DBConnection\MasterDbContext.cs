﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using FA.MasterDB.SQLDatabase.DBModels.Schemes;
using FA.MasterDB.SQLDatabase.DBModels.TertiaryRelated;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.DBConnection
{
    public class MasterDbContext : DbContext
    {
        public MasterDbContext(DbContextOptions<MasterDbContext> options) : base(options)
        {
        }

        // Distributor-related entities
        public DbSet<Distributor> Distributors { get; set; }
        public DbSet<DistributorFieldUserMapping> DistributorFieldUserMappings { get; set; }
        public DbSet<DistributorAddress> DistributorAddresses { get; set; }
        public DbSet<DistributorBeatMapping> DistributorBeatMappings { get; set; }
        public DbSet<BilledtoshippedtoAddress> BilledtoshippedtoAddress { get; set; }

        // Product-related entities
        public DbSet<ProductTable> Products { get; set; }
        public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }
        public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }
        public DbSet<ProductDivision> ProductDivisions { get; set; }
        public DbSet<ProductDisplayCategories> ProductDisplayCategories { get; set; }

        // Location-related entities
        public DbSet<Location> Locations { get; set; }
        public DbSet<OutletSegmentationAttribute> OutletSegmentationAttributes { get; set; }
        public DbSet<Beat> Beats { get; set; }
        public DbSet<Territory> Territories { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<Channel> Channels { get; set; }
        public DbSet<ShopType> ShopTypes { get; set; }
        //public DbSet<CountryInfo> CountryDetails { get; set; }
        public DbSet<Routes> Routes { get; set; }

        // Employee-related entities
        public DbSet<ClientEmployee> ClientEmployees { get; set; }
        public DbSet<CompanyAdmins> CompanyAdmins { get; set; }
        public DbSet<EmployeeProductDivisionMappings> EmployeeProductDivisionMappings { get; set; }
        public DbSet<FAEmployeeBeatMappings> FAEmployeeBeatMappings { get; set; }

        //Company entities
        public DbSet<CompanyFactory> CompanyFactory { get; set; }
        public DbSet<Company> Companies { get; set; }

        //Scheme
        public DbSet<Scheme> Scheme { get; set; }
        public DbSet<SchemeSlab> SchemeSlabs { get; set; }

        // Other entities
        public DbSet<DbExtApiToken> DbExtApiToken { get; set; }
        public DbSet<VanMaster> VanMaster { get; set; }
        public DbSet<ClaimsConfiguration> ClaimsConfiguration { get; set; }
        public DbSet<DMSPOApprovalRights> DMSPOApprovalRights { get; set; }
        public DbSet<TertiaryEntity> TertiaryEntity { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Distributor>().ToTable(
                "FADistributors",
                t => t.HasTrigger("FADistributorsAfterUpdate"));

            // for product
            modelBuilder.Entity<ProductTable>().ToTable("FACompanyProducts");
            modelBuilder.Entity<ProductTable>().Property(b => b.DisplayMRP).HasColumnName("MRP");
            modelBuilder.Entity<CountryInfo>().HasKey(b => b.CountryName);

        }
    }
}
