﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using FileGenerator.Attributes;
using FileGenerator.DataTableHelpers;
using FileGenerator.HelperModels;
using FileGenerator.Interfaces;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace FileGenerator
{
    public class ExcelGeneratorNew
    {
        private readonly bool _showAllColumns;
        private readonly bool _useNomenclature;
        private readonly Dictionary<string, object> companySettings;
        private readonly bool isSettingEnabled;
        private readonly CompanyNomenclatureSpecifier nomenclatureSpecifier;
        private readonly Dictionary<string, bool> selectedCols;
        private readonly Dictionary<string, TableFieldAttribute> colAttributeDic;

        public ExcelGeneratorNew(Dictionary<string, TableFieldAttribute> colAttributeDic = null, CompanyNomenclatureSpecifier nomenclatureSpecifier = null,
            bool showAllColumns = false, bool isSettingEnabled = false, Dictionary<string, object> companySettings = null, Dictionary<string, bool> selectedCols = null)
        {
            this.nomenclatureSpecifier = nomenclatureSpecifier;
            _useNomenclature = nomenclatureSpecifier != null;
            _showAllColumns = showAllColumns;
            this.isSettingEnabled = isSettingEnabled;
            this.companySettings = companySettings;
            this.selectedCols = selectedCols;
            this.colAttributeDic = colAttributeDic;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public void MakeExcelWithFlexibleGroupingNew(long companyId, CreateExcelProperties props)
        {
            var excelPackage = MakeExcelNew(companyId, props);
            excelPackage.Dispose();
        }

        private ExcelPackage MakeExcelNew(long companyId, CreateExcelProperties props)
        {
            props.stream = props.stream ?? new MemoryStream();
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            var shouldCopyFromMemoryStream = false;
            if (props.stream.CanWrite)
            {
                if (props.stream is FileStream)
                {
                    excelPackage = new ExcelPackage(props.stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }

            props.sheetNames = props.sheetNames ?? new[] { "DataSheet_0" };

            var sheetname = props.sheetNames[0];
            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetname);
            if (props.dataList.Count > 0)
            {
                _ = CreateExcelNew(companyId, props, worksheet);
                //GroupExcelData(worksheet, props.data);
            }
            else
            {
                _ = CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }

            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(props.stream);
            }

            if (props.stream != null)
            {
                props.stream.Flush();
                if (props.stream.CanSeek)
                {
                    props.stream.Position = 0;
                }
                else
                {
                    props.stream.Close();
                }
            }

            return excelPackage;
        }

        private ExcelWorksheet CreateExcelNew(long companyId, CreateExcelProperties props, ExcelWorksheet worksheet)
        {
            var col = 1;
            //var columns = data.FirstOrDefault()?.GetType().GetProperties().Where(p =>
            //    Attribute.GetCustomAttribute(p, typeof(TableFieldAttribute)) != null);
            if (props.dataList == null || props.dataList.Count == 0)
            {
                return worksheet;
            }

            var showDataForColumns = new List<string>();
            var colGroupColoring = false;
            var prevgroup = ColGroupType.None;
            var isPrevGroupColored = false;
            var columnsToColor = new List<TableFieldAttribute>();

            foreach (var colProp in props.columnsOrder)
            {
                if (!(colAttributeDic.ContainsKey(colProp)))
                {
                    //derivedMeasures Handling
                    if (props.derivedkpis.Contains(colProp))
                    {
                        showDataForColumns.Add(colProp);
                        worksheet.Cells[1, col++].Value = colProp;
                    }

                    continue;
                }

                var colAttribute = colAttributeDic[colProp];
                if (colAttribute == null)
                {
                    continue;
                }
                //var Tattr = Attribute.GetCustomAttribute(colProp, typeof(TableFieldAttribute));
                //if (Tattr is TableFieldAttribute excelAttrOfT)
                //{
                var columnDisplayName = colAttribute.ColumnName;
                if (_showAllColumns || colAttribute.ColumnRequirement != Requirement.HideIfNull ||
                    !CheckIfColNullNew(colProp, props.dataList))
                {
                    if (colAttribute.ColumnRequirement == Requirement.SettingBased &&
                        !CheckIfColRequiredNew(colProp, props.dataList))
                    {
                        showDataForColumns.Add(colProp);
                    }
                    else if (!_showAllColumns && colAttribute.ColumnRequirement == Requirement.HideIfZero &&
                             CheckIfColZeroNew(colProp, props.dataList))
                    {
                        showDataForColumns.Add(colProp);
                    }
                    //else if (colAttribute.ColumnRequirement == Requirement.SpecificSettingBased &&
                    //         !CheckIfColRequiredBySetting(colProp, colAttribute.CompanySettingsToCheck))
                    //{
                    //    showDataForHeader.Add(i++, false);
                    //}
                    else
                    {
                        if (!string.IsNullOrWhiteSpace(colAttribute.CellFormat))
                        {
                            worksheet.Column(col).Style.Numberformat.Format = colAttribute.CellFormat;
                        }

                        if (colAttribute.ColGroupType != ColGroupType.None)
                        {
                            if (prevgroup == colAttribute.ColGroupType)
                            {
                                if (isPrevGroupColored)
                                {
                                    worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                    worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                    isPrevGroupColored = true;
                                }
                            }
                            else
                            {
                                if (!isPrevGroupColored)
                                {
                                    worksheet.Column(col).Style.Fill.PatternType = ExcelFillStyle.Solid;
                                    worksheet.Column(col).Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                                    isPrevGroupColored = true;
                                }
                                else
                                {
                                    isPrevGroupColored = false;
                                }
                            }

                            prevgroup = colAttribute.ColGroupType;
                            colGroupColoring = true;
                        }
                        else
                        {
                            prevgroup = ColGroupType.None;
                            isPrevGroupColored = false;
                        }

                        if (_useNomenclature && colAttribute.NomenclatureRequirement)
                        {
                            if (props.useNomenclatureUpdated && colAttribute.NomenclatureUpdated != null)
                            {
                                var j = 0;
                                var listforheader = columnDisplayName.Split(' ').ToList();
                                var listforNewHeader = columnDisplayName.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                    listforNewHeader[j] = "'" + nomenclaturename + "' " +
                                                          colAttribute.NomenclatureUpdated;
                                    j++;
                                }

                                columnDisplayName = string.Join(" ", listforNewHeader);
                            }
                            else if (colAttribute.UseAdditionalNomenclature)
                            {
                                var j = 0;
                                var listforheader = columnDisplayName.Split(' ').ToList();
                                var listforNewHeader = columnDisplayName.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderNameConsideringAN(item);
                                    listforNewHeader[j] = nomenclaturename;
                                    j++;
                                }

                                columnDisplayName = string.Join(" ", listforNewHeader);
                            }
                            else
                            {
                                var j = 0;
                                var listforheader = columnDisplayName.Split(' ').ToList();
                                var listforNewHeader = columnDisplayName.Split(' ').ToList();
                                foreach (var item in listforheader)
                                {
                                    var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                                    listforNewHeader[j] = nomenclaturename;
                                    j++;
                                }

                                columnDisplayName = string.Join(" ", listforNewHeader);
                            }
                        }

                        if (!string.IsNullOrWhiteSpace(colAttribute.ConditionalFormat))
                        {
                            columnsToColor.Add(colAttribute);
                        }

                        worksheet.Cells[1, col++].Value = columnDisplayName;
                        showDataForColumns.Add(colProp);
                    }
                }
            }

            //populate the data
            var totalCols = col - (col == 0 ? 0 : 1);
            var rowIndex = 2;
            foreach (var item in props.dataList)
            {
                col = 1;
                if (rowIndex % 2 == 0 && !colGroupColoring)
                {
                    worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[rowIndex, 1, rowIndex, totalCols].Style.Fill.BackgroundColor
                        .SetColor(Color.LightGray);
                }

                foreach (var colProp in showDataForColumns)
                {
                    var dataValue = item.TryGetValue(colProp, out var colVal) ? colVal : null;

                    //derived Measures handling
                    if (!(colAttributeDic.ContainsKey(colProp)))
                    {
                        worksheet.Cells[rowIndex, col++].Value = dataValue;
                        continue;
                    }

                    var columnAttribute = colAttributeDic[colProp];
                    if (dataValue != null)
                    {
                        worksheet.Cells[rowIndex, col].Value = dataValue;

                        if (!string.IsNullOrWhiteSpace(dataValue.ToString()) &&
                            !string.IsNullOrWhiteSpace(columnAttribute.HyperLinkText))
                        {
                            worksheet.Cells[rowIndex, col].Hyperlink =
                                new Uri(dataValue.ToString(), UriKind.Absolute);
                            worksheet.Cells[rowIndex, col].Value = columnAttribute.HyperLinkText;
                            worksheet.Cells[rowIndex, col].Style.Font.Color.SetColor(Color.Blue);
                        }
                        else
                        {
                            worksheet.Cells[rowIndex, col].Value = dataValue;
                        }
                    }

                    col++;
                }

                rowIndex++;
            }

            var totalRows = props.dataList.Count();

            //if (showGrandTotal)
            //{
            //    worksheet.Row(totalRows + 1).Style.Font.Bold = true;
            //    worksheet.Cells[totalRows + 1, 1].Value = "Grand Total";
            //}

            //Styling
            worksheet.Row(1).Style.Font.Bold = true;

            var modelTable = worksheet.Cells[1, 1, totalRows + 1, totalCols];
            //Assign borders
            modelTable.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            modelTable.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            modelTable.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            modelTable.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[1, 1, 1, totalCols].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));

            try
            {
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
            catch
            {
            }

            FormattingHelpers.ApplyConditionalFormattingFromColProperty(worksheet, columnsToColor, totalCols, totalRows);

            //if (groupData != null)
            //{
            //    worksheet.InsertRow(1, 1);
            //    var startIndex = groupData.First().Key.Split(':')[0];
            //    var lastIndex = groupData.Last().Key.Split(':')[1];
            //    var destination = startIndex + ":" + lastIndex;
            //    var source = destination.Replace('1', '2');
            //    worksheet.Cells[source].Copy(worksheet.Cells[destination]);

            //    foreach (var x in groupData)
            //    {
            //        worksheet.Cells[x.Key].Merge = true;
            //        worksheet.Cells[x.Key].Value = x.Value;
            //    }
            //}

            return worksheet;
        }

        //pivoting

        public static DataTable addDerivedColumns(DataTable dt, List<string> derivedkpis = null)
        {
            if (derivedkpis != null && derivedkpis.Count != 0)
            {
                foreach (var x in derivedkpis)
                {
                    dt.Columns.Add(x, typeof(string));
                }

                foreach (DataRow row in dt.Rows)
                {
                    var derivedColstring = (string)row["derivedMeasures"];
                    var derivedDic = JsonConvert.DeserializeObject<Dictionary<string, string>>(derivedColstring);
                    foreach (var x in derivedkpis)
                    {
                        row[x] = derivedDic[x];
                    }
                }
            }

            return dt;
        }

        private static List<string> arrangeCOlumns(string headerType, List<string> columnsToArrange)
        {
            switch (headerType)
            {
                case "Month":
                    var month = new List<string>
                        { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
                    columnsToArrange = columnsToArrange.OrderBy(x => month.IndexOf(x)).ToList();
                    break;

                case "Week":
                    var Week = new List<string>
                        { "Week_1", "Week_2", "Week_3", "Week_4", "Week_5", "Week_6", "Total" };
                    columnsToArrange = Week.Where(m => columnsToArrange.Any(o => o == m)).ToList();
                    break;

                case "Date":
                    if (columnsToArrange.Any(a => DateTime.TryParse(a, out _)))
                    {
                        columnsToArrange = columnsToArrange.OrderBy(s =>
                            DateTime.ParseExact(s, "dd/MM/yyyy", new CultureInfo("en-US"))).ToList();
                    }

                    break;
                case "InvoiceDate":
                    if (columnsToArrange.Any(a => DateTime.TryParse(a, out _)))
                    {
                        columnsToArrange = columnsToArrange.OrderBy(s =>
                            DateTime.ParseExact(s, "dd/MM/yyyy", new CultureInfo("en-US"))).ToList();
                    }

                    break;
            }

            return columnsToArrange;
        }

        private static DataTable DataTableForSingleOrPartialPivot(DataTable dataTable, PivotDetails pivotColumn,
            string delimiter, string defaultGroupColumnName = null, Dictionary<string, int> sortingDictionaryList = null)
        {
            // Pivot the DataTable
            var pivotTable = new DataTable();
            var allcolumns = dataTable.Columns.Cast<DataColumn>().Select(d => d.ColumnName).ToList();
            var unpivotedColumns = allcolumns.Where(c => c != "Id" && c != pivotColumn.ParentColumn && c != pivotColumn.SecondaryParentColumn && !pivotColumn.ValueColumns.Contains(c)).ToList();
            foreach (var c in unpivotedColumns)
            {
                pivotTable.Columns.Add(c, dataTable.Columns[c].DataType);
            }

            // Get distinct ParentValues
            var distinctParentValues = dataTable.AsEnumerable().Select(r => r.Field<string>(pivotColumn.ParentColumn)).Distinct().ToList();
            distinctParentValues = arrangeCOlumns(pivotColumn.ParentColumn, distinctParentValues);
            // for Single Pivot columns for each distinct ParentValues
            foreach (var parentValue in distinctParentValues)
            {
                foreach (var c in pivotColumn.ValueColumns)
                {
                    pivotTable.Columns.Add(parentValue + delimiter + c, dataTable.Columns[c].DataType);
                }
            }

            foreach (var rowGroup in dataTable.Rows.Cast<DataRow>().GroupBy(r => r["Id"]))
            {
                var pivotRow = pivotTable.NewRow();
                var defaultRow = rowGroup.FirstOrDefault();
                foreach (var c in unpivotedColumns)
                {
                    pivotRow[c] = defaultRow[c];
                }

                foreach (var row in rowGroup)
                {
                    var parentValue = row[pivotColumn.ParentColumn].ToString();

                    foreach (var c in pivotColumn.ValueColumns)
                    {
                        //Addition Functionality is Failing with null Values. Will handle it when we need it
                        //if (parentValue != defaultGroupColumnName && pivotTable.Columns[parentValue + delimiter + c].IsNumeric())
                        //    pivotRow[parentValue + delimiter + c] = AddTwoObjectsOfSameType(pivotRow[parentValue + delimiter + c], row[c]);
                        //else
                        pivotRow[parentValue + delimiter + c] = row[c];
                    }
                }

                pivotTable.Rows.Add(pivotRow);
            }

            //Remove Columns with default Text
            foreach (var c in pivotColumn.ValueColumns)
            {
                if (pivotTable.Columns.Contains(defaultGroupColumnName + delimiter + c))
                {
                    pivotTable.Columns.Remove(defaultGroupColumnName + delimiter + c);
                }
            }

            return (pivotTable);
        }

        private static DataTable DataTableForDoublePivot(DataTable dataTable, PivotDetails pivotColumn,
            string delimiter, string defaultGroupColumnName = null, Dictionary<string, int> sortingDictionaryList = null)
        {
            // Pivot the DataTable
            var pivotTable = new DataTable();
            var allcolumns = dataTable.Columns.Cast<DataColumn>().Select(d => d.ColumnName).ToList();
            var unpivotedColumns = allcolumns.Where(c => c != "Id" && c != pivotColumn.ParentColumn && c != pivotColumn.SecondaryParentColumn && !pivotColumn.ValueColumns.Contains(c)).ToList();
            foreach (var c in unpivotedColumns)
            {
                pivotTable.Columns.Add(c, dataTable.Columns[c].DataType);
            }

            // Get distinct ParentValues
            var distinctParentValues = dataTable.AsEnumerable().Select(r => r.Field<string>(pivotColumn.ParentColumn)).Distinct().ToList();
            var distinctSecParentValues = dataTable.AsEnumerable().Select(r => r.Field<string>(pivotColumn.SecondaryParentColumn)).Distinct().ToList();
            distinctParentValues = arrangeCOlumns(pivotColumn.ParentColumn, distinctParentValues);
            distinctSecParentValues = arrangeCOlumns(pivotColumn.SecondaryParentColumn, distinctSecParentValues);

            // for dobulePivoting
            foreach (var secParentValue in distinctSecParentValues)
            {
                foreach (var parentValue in distinctParentValues)
                {
                    foreach (var c in pivotColumn.ValueColumns)
                    {
                        pivotTable.Columns.Add(secParentValue + delimiter + parentValue + delimiter + c, dataTable.Columns[c].DataType);
                    }
                }
            }

            foreach (var rowGroup in dataTable.Rows.Cast<DataRow>().GroupBy(r => r["Id"]))
            {
                var pivotRow = pivotTable.NewRow();
                var defaultRow = rowGroup.FirstOrDefault();
                foreach (var c in unpivotedColumns)
                {
                    pivotRow[c] = defaultRow[c];
                }

                foreach (var row in rowGroup)
                {
                    var secParentValue = row[pivotColumn.SecondaryParentColumn].ToString();
                    var parentValue = row[pivotColumn.ParentColumn].ToString();

                    foreach (var c in pivotColumn.ValueColumns)
                    {
                        //Addition Functionality is Failing with null Values. Will handle it when we need it
                        //if (parentValue != defaultGroupColumnName && pivotTable.Columns[parentValue + delimiter + c].IsNumeric())
                        //    pivotRow[parentValue + delimiter + c] = AddTwoObjectsOfSameType(pivotRow[parentValue + delimiter + c], row[c]);
                        //else
                        pivotRow[secParentValue + delimiter + parentValue + delimiter + c] = row[c];
                    }
                }

                pivotTable.Rows.Add(pivotRow);
            }

            //Remove Columns with default Text
            foreach (var c in pivotColumn.ValueColumns)
            {
                if (pivotTable.Columns.Contains(defaultGroupColumnName + delimiter + c))
                {
                    pivotTable.Columns.Remove(defaultGroupColumnName + delimiter + c);
                }
            }

            return (pivotTable);
        }

        public void MakeExcelToStream(CreateExcelProperties props, Stream stream, string sheetName)
        {
            var excelPackage = MakeExcel(props, stream, sheetName);
            excelPackage.Dispose();
        }

        private ExcelPackage MakeExcel(CreateExcelProperties props, Stream stream, string sheetName)
        {
            ExcelPackage excelPackage;
            MemoryStream memoryStream = null;
            var shouldCopyFromMemoryStream = false;
            stream = stream ?? new MemoryStream();
            if (stream.CanWrite)
            {
                if (stream is FileStream)
                {
                    excelPackage = new ExcelPackage(stream);
                }
                else
                {
                    memoryStream = new MemoryStream();
                    shouldCopyFromMemoryStream = true;
                    excelPackage = new ExcelPackage(memoryStream);
                }
            }
            else
            {
                throw new Exception("Stream should be Writable!");
            }

            var worksheet = excelPackage.Workbook.Worksheets.Add(sheetName);
            if (props.dataList == null || props.dataList.Count == 0)
            {
                CreateExcelForErrorAndNoData(worksheet, "Data not available for this request");
            }
            else
            {
                UpdatePivotExcelSheet(props, worksheet);
                //if (isUsingDMS && dmsCumulativeData.Count > 0)
                //{
                //    var pivotIndex = pivotOrder.Select((s, i) => new { s, i }).ToDictionary(d => d.s, d => d.i);
                //    for (var i = 0; i < dmsCumulativeData.Count; i++)
                //        dmsCumulativeData[i] = dmsCumulativeData[i].OrderBy(d =>
                //            pivotIndex.ContainsKey(d.HeaderKey ?? "") ? pivotIndex[d.HeaderKey] : -1).ToList();
                //    GroupDMSExcelDataWithDoublePivot(worksheet, dmsCumulativeData);
                //}
            }

            excelPackage.Save();
            if (shouldCopyFromMemoryStream)
            {
                memoryStream.Flush();
                memoryStream.Position = 0;
                memoryStream.CopyTo(stream);
            }

            stream.Flush();
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }
            else
            {
                stream.Close();
            }

            return excelPackage;
        }

        private bool CheckIfColRequiredBySetting(string col, CompanySetting[] companySettingsToCheck)
        {
            if (companySettings != null && companySettingsToCheck != null && companySettingsToCheck.Length > 0)
            {
                foreach (var companySetting in companySettingsToCheck)
                {
                    switch (companySetting)
                    {
                        case CompanySetting.UsesPositionCodes:
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !(bool)companySettings[companySetting.ToString()])
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.HighestPositionLevel:
                            PositionCodeLevel colPosLevel, highestPosLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !Enum.TryParse("Level" + col.Split(' ')[1], out colPosLevel) ||
                                !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                    out highestPosLevel) || highestPosLevel > colPosLevel)
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.HighestGeoHierarchy:
                            GeographyLevel colGeoLevel, highestGeoLevel;
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !Enum.TryParse(col, out colGeoLevel) ||
                                !Enum.TryParse(companySettings[companySetting.ToString()].ToString(),
                                    out highestGeoLevel) || highestGeoLevel < colGeoLevel)
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.UsesFAUnify:
                            if (!companySettings.ContainsKey(companySetting.ToString()) ||
                                !(bool)companySettings[companySetting.ToString()])
                            {
                                return false;
                            }

                            break;

                        case CompanySetting.NotApplicable:
                        default:
                            return true;
                    }
                }
            }

            return true;
        }

        private void CreateExcelFromDataTable(ExcelWorksheet worksheet, DataTable dataTable)
        {
            //columnsOrder - including pivot extra columns
            var dataTableCols = dataTable.Columns.Cast<DataColumn>().Select(column => column.ColumnName).ToList();
            var ambiguityColDisplayNameDic = new Dictionary<string, string>();
            foreach (var col in dataTableCols)
            {
                var isPivotCol = col.Contains('#');
                var colValueKey = isPivotCol ? col.Substring(col.LastIndexOf('#') + 1) : col;
                var colPivotKey = isPivotCol ? col.Substring(0, col.LastIndexOf('#') + 1) : "";
                var colValueKeyAttribute = colAttributeDic.ContainsKey(colValueKey) ? colAttributeDic[colValueKey] : null;
                var colValueKeyDisplayName = colValueKeyAttribute != null ? colValueKeyAttribute.ColumnName : colValueKey;
                if (colValueKeyAttribute != null)
                {
                    if (!isPivotCol && !_showAllColumns && colValueKeyAttribute.ColumnRequirement == Requirement.HideIfNull && dataTable.Rows
                            .Cast<DataRow>().All(dr => string.IsNullOrEmpty(dr[col].ToString())))
                    {
                        dataTable.Columns.Remove(col);
                    }
                    else if (!isPivotCol && !_showAllColumns && colValueKeyAttribute.ColumnRequirement == Requirement.HideIfZero &&
                             Convert.ToDouble(dataTable.Compute("Max(" + col + ")", string.Empty)) == 0)
                    {
                        dataTable.Columns.Remove(col);
                    }
                    else if (!_showAllColumns && colValueKeyAttribute.ColumnRequirement == Requirement.SpecificSettingBased &&
                             !CheckIfColRequiredBySetting(col, colValueKeyAttribute.CompanySettingsToCheck))
                    {
                        dataTable.Columns.Remove(col);
                    }

                    if (_useNomenclature && colValueKeyAttribute.NomenclatureRequirement)
                    {
                        var j = 0;
                        var listforheader = colValueKeyDisplayName.Split(' ').ToList();
                        var listforNewHeader = colValueKeyDisplayName.Split(' ').ToList();
                        foreach (var item in listforheader)
                        {
                            var nomenclaturename = nomenclatureSpecifier.GetHeaderName(item);
                            listforNewHeader[j] = nomenclaturename;
                            j++;
                        }

                        colValueKeyDisplayName = string.Join(" ", listforNewHeader);

                        //if (colProp.Name == VerticalTotalColumn)
                        //{
                        //    VerticalTotalColumn = columnname;
                        //    if (colProp.Name != columnname && data.Columns.Contains(columnname))
                        //        VerticalTotalColumn = "$$" + columnname;
                        //}
                    }

                    var columnDisplayName = colPivotKey + colValueKeyDisplayName;
                    if (dataTable.Columns.Contains(col))
                    {
                        //handle case when nomenclature name of curr col is same as other col name of table
                        if (col != columnDisplayName && dataTable.Columns.Contains(columnDisplayName))
                        {
                            ambiguityColDisplayNameDic.Add(col, columnDisplayName);
                        }
                        else
                        {
                            dataTable.Columns[col].ColumnName = columnDisplayName;
                        }
                    }
                }
            }

            //handle case when nomenclature name of curr col is same as other col name of table
            foreach (var item in ambiguityColDisplayNameDic)
            {
                dataTable.Columns[item.Key].ColumnName = "TempColName_" + item.Key;
            }

            foreach (var item in ambiguityColDisplayNameDic)
            {
                dataTable.Columns["TempColName_" + item.Key].ColumnName = item.Value;
            }

            worksheet.Cells.LoadFromDataTable(dataTable, PrintHeaders: true);
        }

        private static void FormatWorkSheetWithSinglePivot(ExcelWorksheet worksheet)
        {
            int headerRow = 2, columnCount = worksheet.Dimension.Columns;

            using (var range = worksheet.Cells[headerRow, 1, headerRow, columnCount])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
            }

            worksheet.Cells[worksheet.Dimension.Address].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            var border = worksheet.Cells[worksheet.Dimension.Address].Style.Border;
            border.Left.Style = ExcelBorderStyle.Thin;
            border.Right.Style = ExcelBorderStyle.Thin;
            border.Top.Style = ExcelBorderStyle.Thin;
            border.Bottom.Style = ExcelBorderStyle.Thin;
        }

        private static void FormatWorkSheetWithDoublePivot(ExcelWorksheet worksheet)
        {
            int headerRow = 3, pivotSecParentRow = 1, columnCount = worksheet.Dimension.Columns;

            using (var range = worksheet.Cells[headerRow, 1, headerRow, columnCount])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(0, 150, 207));
            }

            worksheet.Cells[pivotSecParentRow, 1, pivotSecParentRow, columnCount].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            worksheet.Cells[worksheet.Dimension.Address].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            var border = worksheet.Cells[worksheet.Dimension.Address].Style.Border;
            border.Left.Style = ExcelBorderStyle.Thin;
            border.Right.Style = ExcelBorderStyle.Thin;
            border.Top.Style = ExcelBorderStyle.Thin;
            border.Bottom.Style = ExcelBorderStyle.Thin;
        }

        public static void AutoFitColumns(ExcelWorksheet worksheet)
        {
            try
            {
                if (worksheet.Dimension != null)
                {
                    worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                }
            }
            catch
            {
                Console.WriteLine("Failed to AutoFit Columns");
            }
        }

        private static void SetMergingColProperties(ExcelRange mergedColumn)
        {
            mergedColumn.Merge = true;
            mergedColumn.Style.Fill.PatternType = ExcelFillStyle.Solid;
            mergedColumn.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
            mergedColumn.Style.Border.Top.Style = ExcelBorderStyle.Thin;
            mergedColumn.Style.Border.Left.Style = ExcelBorderStyle.Thin;
            mergedColumn.Style.Border.Right.Style = ExcelBorderStyle.Thin;
            mergedColumn.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            mergedColumn.Style.Font.Bold = true;
        }

        private static void CreateSingleOrPartialPivotRow(ExcelWorksheet worksheet, string delimiter)
        {
            var headerRow = worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column];
            var headers = headerRow.Select(cell => cell.Text).ToList();
            worksheet.InsertRow(1, 1);
            var currPivotColumnName = "";
            int mergeColStart = 0, mergeColEnd = 0, lastColIndex = headers.Count;

            for (var i = 1; i <= lastColIndex; i++)
            {
                var header = headers[i - 1];
                if (header.Contains(delimiter))
                {
                    var headerPivot = header.Split(delimiter[0])[0];
                    var headerMeasure = header.Split(delimiter[0])[1];
                    // merging all previous batch except the last batch
                    if (currPivotColumnName != headerPivot)
                    {
                        mergeColEnd = i - 1;
                        if (mergeColStart != 0)
                        {
                            worksheet.Cells[1, mergeColStart].Value = currPivotColumnName;
                            var mergedColumn = worksheet.Cells[1, mergeColStart, 1, mergeColEnd];
                            SetMergingColProperties(mergedColumn);
                        }

                        currPivotColumnName = headerPivot;
                        mergeColStart = i;
                    }

                    //merging last batch
                    if (i == lastColIndex)
                    {
                        worksheet.Cells[1, mergeColEnd + 1].Value = headerPivot;
                        var mergedColumn = worksheet.Cells[1, mergeColEnd + 1, 1, i];
                        SetMergingColProperties(mergedColumn);
                    }

                    worksheet.Cells[2, i].Value = headerMeasure;
                }
                else
                {
                    worksheet.Cells[2, i].Value = header;
                }
            }
        }

        private static void CreateDoublePivotRow(ExcelWorksheet worksheet, string delimiter)
        {
            var headerRow = worksheet.Cells[1, 1, 1, worksheet.Dimension.End.Column];
            var headers = headerRow.Select(cell => cell.Text).ToList();
            worksheet.InsertRow(1, 2);
            string lastParentPivot = "", lastSecParentPivot = "";
            int mergeParentColStart = 0, mergeParentColEnd = 0, mergeSecParentColStart = 0, mergeSecParentColEnd = 0, lastColIndex = headers.Count;

            for (var i = 1; i <= lastColIndex; i++)
            {
                var header = headers[i - 1];
                if (header.Contains(delimiter))
                {
                    var secParentPivot = header.Split(delimiter[0])[0];
                    var parentPivot = header.Split(delimiter[0])[1];
                    var pivotValue = header.Split(delimiter[0])[2];

                    // merging all previous parentPivot batch except the last batch
                    if (lastParentPivot != parentPivot)
                    {
                        mergeParentColEnd = i - 1;
                        if (mergeParentColStart != 0)
                        {
                            worksheet.Cells[2, mergeParentColStart].Value = lastParentPivot;
                            var mergedColumn = worksheet.Cells[2, mergeParentColStart, 2, mergeParentColEnd];
                            SetMergingColProperties(mergedColumn);
                        }

                        lastParentPivot = parentPivot;
                        mergeParentColStart = i;
                    }

                    // merging all previous secParentPivot batch except the last batch
                    if (lastSecParentPivot != secParentPivot)
                    {
                        mergeSecParentColEnd = i - 1;
                        if (mergeSecParentColStart != 0)
                        {
                            worksheet.Cells[1, mergeSecParentColStart].Value = lastSecParentPivot;
                            var mergedColumn = worksheet.Cells[1, mergeSecParentColStart, 1, mergeSecParentColEnd];
                            SetMergingColProperties(mergedColumn);
                        }

                        lastSecParentPivot = secParentPivot;
                        mergeSecParentColStart = i;
                    }

                    //merging last batch of parentPivot
                    if (i == lastColIndex)
                    {
                        worksheet.Cells[2, mergeParentColEnd + 1].Value = parentPivot;
                        var mergedColumn = worksheet.Cells[2, mergeParentColEnd + 1, 2, i];
                        SetMergingColProperties(mergedColumn);
                    }

                    //merging last batch of secParentPivot
                    if (i == lastColIndex)
                    {
                        worksheet.Cells[1, mergeSecParentColEnd + 1].Value = secParentPivot;
                        var mergedColumn = worksheet.Cells[1, mergeSecParentColEnd + 1, 1, i];
                        SetMergingColProperties(mergedColumn);
                    }

                    worksheet.Cells[3, i].Value = pivotValue;
                }
                else
                {
                    worksheet.Cells[3, i].Value = header;
                }
            }
        }

        private ExcelWorksheet UpdatePivotExcelSheet(CreateExcelProperties props, ExcelWorksheet workSheet)
        {
            var ConvertToDataTable = new ListToDataTableConverter();
            var pivotTable = new DataTable();
            var useSingleOrPartialPivot = props.PivotDetails.ParentColumn != null && props.PivotDetails.SecondaryParentColumn == null;
            var useDoublePivot = props.PivotDetails.ParentColumn != null && props.PivotDetails.SecondaryParentColumn != null;
            props.columnsOrder.Add("Id");
            var dt = ListToDataTableConverter.GetDataTableFromListOfDicNew(props.dataList, props.columnsOrder);
            //dt = addDerivedColumns(dt, props.derivedkpis);
            if (props.columnsToDeleteFromPivot != null && props.columnsToDeleteFromPivot.Count() > 0)
            {
                foreach (var col in props.columnsToDeleteFromPivot)
                {
                    dt.Columns.Remove(col);
                    props.PivotDetails.ValueColumns = props.PivotDetails.ValueColumns.Where(v => v != col).ToList();
                }
            }

            if (useSingleOrPartialPivot)
            {
                pivotTable = DataTableForSingleOrPartialPivot(dt, props.PivotDetails, "#");
                CreateExcelFromDataTable(workSheet, pivotTable);
                CreateSingleOrPartialPivotRow(workSheet, "#");
                FormatWorkSheetWithSinglePivot(workSheet);
            }
            else if (useDoublePivot)
            {
                pivotTable = DataTableForDoublePivot(dt, props.PivotDetails, "#");
                CreateExcelFromDataTable(workSheet, pivotTable);
                CreateDoublePivotRow(workSheet, "#");
                FormatWorkSheetWithDoublePivot(workSheet);
            }

            AutoFitColumns(workSheet);
            return workSheet;
        }

        public static ExcelWorksheet CreateExcelForErrorAndNoData(ExcelWorksheet worksheet, string errorMessage)
        {
            worksheet.Cells[1, 1].LoadFromText(errorMessage);
            return worksheet;
        }

        private static bool CheckIfColNullNew(string key, List<Dictionary<string, object>> listOfData)
        {
            foreach (var item in listOfData)
            {
                if (item.ContainsKey(key) && item[key] != null && !string.IsNullOrWhiteSpace(item[key].ToString()))
                {
                    return false;
                }
            }

            return true;
        }

        private static bool CheckIfColZeroNew(string key, List<Dictionary<string, object>> listOfData)
        {
            decimal value;
            double value2;
            foreach (var item in listOfData)
            {
                if (item[key] != null && decimal.TryParse(item[key].ToString(), out value) &&
                    value > 0)
                {
                    return false;
                }
                else if (item[key] != null &&
                         double.TryParse(item[key].ToString(), out value2) && value2 > 0)
                {
                    return false;
                }
            }

            return true;
        }

        private bool CheckIfColRequiredNew(string key, List<Dictionary<string, object>> listOfData)
        {
            if (isSettingEnabled)
            {
                foreach (var item in listOfData)
                {
                    if (item[key] != null && !string.IsNullOrWhiteSpace(item[key].ToString()))
                    {
                        return true;
                    }
                    else
                    {
                        if (_showAllColumns)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }

            return false;
        }
    }
}
