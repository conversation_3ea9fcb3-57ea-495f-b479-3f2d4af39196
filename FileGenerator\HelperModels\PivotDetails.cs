﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using System.Linq;

namespace FileGenerator.HelperModels
{
    public class PivotDetails
    {
        public string ParentColumn { get; set; }
        public string SecondaryParentColumn { get; set; }
        public List<string> ValueColumns { get; set; }

        public string ValueColumn
        {
            get => ValueColumns.FirstOrDefault();
            set => ValueColumns = new List<string> { value };
        }
    }
}
