﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    [Table("BilledtoshippedtoAddress")]
    public class BilledtoshippedtoAddress
    {
        public long Id { get; set; }

        public long OutletId { get; set; }

        public string Address { get; set; }

        public string IntegerationId1 { get; set; }

        public string IntegreationId2 { get; set; }

        public bool IsActive { get; set; }

        public string AddressDetail { get; set; }

        public string Area { get; set; }

        public string City { get; set; }

        public string PinCode { get; set; }
    }
}
