﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    [Table("ClientEmployeeTokens")]
    public class EmployeeToken
    {
        public long Id { get; set; }

        [Column("ClientEmployeeId")]
        public long EmployeeId { get; set; }

        public string Token { get; set; }

        public bool IsActive { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime Time { get; set; }

        public long? DeviceId { get; set; }

        public virtual ClientEmployee ClientEmployee { get; set; }
    }
}
