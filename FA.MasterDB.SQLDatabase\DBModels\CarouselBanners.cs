﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class CarouselBanners
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public int Sequence { get; set; }

        public string BannerImageGUID { get; set; }

        public string ProductDivisionArray { get; set; }

        public string BannerRedirectUrl { get; set; }

        public DateTime? CreatedAt { get; set; }

        public DateTime? LastUpdatedAt { get; set; }

        public string RegionIds { get; set; }

        [Column("DynamicCarouselBannerId")]
        public long? DynamicCarouselBannerId { get; set; }

        [Column("IsActive")]
        public bool IsActive { get; set; }

        [Column("BannerRedirectImageGUID")]
        public string BannerRedirectImageGUID { get; set; }

        [Column("IsBannerRedirectUrl")]
        public bool IsBannerRedirectUrl { get; set; }
    }
}
