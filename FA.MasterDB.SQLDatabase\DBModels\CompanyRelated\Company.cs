﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    public class Company
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public bool Deleted { get; set; }

        public string ImageId { get; set; }

        public string PreloaderBgImageId { get; set; }

        public string PreloaderIcon { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string BeatShortName { get; set; }

        public string ShortName { get; set; }

        public string EmployeeShortName { get; set; }

        public string BaseUrlSfApp { get; set; }

        [Column("ProjectStage")]
        public ProjectStage ProjectStage { get; set; }
    }
}
