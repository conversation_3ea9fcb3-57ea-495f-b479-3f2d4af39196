﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    [Table("CompanyAdmins")]
    public class CompanyAdmin
    {
        [Column("Company")]
        public long CompanyId { get; set; }

        public string EmailId { get; set; }

        public Guid Guid { get; set; }

        public long Id { get; set; }

        public bool IsBillable { get; set; }

        public bool IsDeactive { get; set; }

        public Guid? LoginGuid { get; set; }

        public string Name { get; set; }

        public string PhoneNo { get; set; }

        public string PositionInCompany { get; set; }

        public long? RegionalParentId { get; set; }

        public PortalUserRole? RegionaParentUserRole { get; set; }

        public PortalUserRole UserRole { get; set; }
    }
}
