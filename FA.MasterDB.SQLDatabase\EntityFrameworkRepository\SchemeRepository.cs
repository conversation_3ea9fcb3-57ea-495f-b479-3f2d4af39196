﻿// Copyright (c) FieldAssist. All Rights Reserved.

using AutoMapper;
using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.Repositories;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository
{
    public class SchemeRepository : ISchemeRepository
    {
        private readonly MasterDbContext db;
        private readonly IMapper _mapper;

        public SchemeRepository(MasterDbContext db,
            IMapper mapper)
        {
            this.db = db;
            _mapper = mapper;
        }
        public async Task<List<SchemeEntity>> GetSchemes
            (long companyId)
        {
            return await db.Scheme.Where(s =>
                s.CompanyId == companyId)
                .Select(s =>
                _mapper.Map<SchemeEntity>(s))
                .ToListAsync();
        }

        public async Task<List<SchemeEntity>> GetSchemesWithSlab
            (long companyId)
        {
            return await db.Scheme.Include(s => s.SchemeSlabs).Where(s =>
                s.CompanyId == companyId)
                .Select(s =>
                _mapper.Map<SchemeEntity>(s))
                .ToListAsync();
        }
    }
}
