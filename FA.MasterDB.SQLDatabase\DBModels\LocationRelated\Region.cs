﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("Regions")]
    public class Region
    {
        public long Id { get; set; }
        public string ErpId { get; set; }
        public string Name { get; set; }
        public long? ZoneId { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
        public string ShortRegionCode { get; set; }
        public Zone Zone { get; set; }
    }
}
