﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Interface;
using FA.MasterDB.Core.Service;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace FA.MasterDB.Core.Dependency
{
    public class ConfigureDependency
    {
        public static void SetUp(IServiceCollection serviceProvider, IConfiguration configuration)
        {
            serviceProvider.AddScoped<ILocationService, LocationService>();
        }
    }
}
