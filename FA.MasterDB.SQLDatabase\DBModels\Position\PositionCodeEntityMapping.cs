﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Position
{
    public class PositionCodeEntityMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long PositionCodeId { get; set; }

        public long EntityId { get; set; }

        [Column("IsDetached")]
        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public PositionCode PositionCode { get; set; }

        public PositionCodeLevel? EntityLevel { get; set; }
    }
}
