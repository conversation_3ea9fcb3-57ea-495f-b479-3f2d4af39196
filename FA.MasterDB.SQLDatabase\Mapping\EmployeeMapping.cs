﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class EmployeeMapping
    {
        public static EmployeeEntity CreateEmployeeObject(ClientEmployee employee)
        {
            return new EmployeeEntity
            {
                ContactNumer = employee.ContactNo,
                Id = employee.Id,
                Name = employee.Name,
                EsmErpId = employee.ClientSideId
            };
        }

        public static EmployeeWithParent CreateObject_CreateEmployeeWithParent(ClientEmployee clientEmployee)
        {
            return BuildEmployeeWithParent(clientEmployee);
        }

        private static EmployeeWithParent BuildEmployeeWithParent(ClientEmployee clientEmployee)
        {
            if (clientEmployee == null)
            {
                return null;
            }

            return new EmployeeWithParent
            {
                CompanyId = clientEmployee.Company,
                Id = clientEmployee.Id,
                Name = clientEmployee.Name,
                UserRole = clientEmployee.UserRole,
                UserType = clientEmployee.UserType,
                Rank = clientEmployee.Rank,
                EmailId = clientEmployee.EmailId,
                ClientSideId = clientEmployee.ClientSideId,
                IsVacant = clientEmployee.isVacant,
                Parent = BuildEmployeeWithParent(clientEmployee.Parent)
            };
        }
    }
}
