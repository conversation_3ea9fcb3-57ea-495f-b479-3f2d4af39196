﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using FastMember;

namespace FileGenerator.DataTableHelpers
{
    public class ListToDataTableConverter
    {
        public static DataTable GetDataTableFromList<T>(IEnumerable<T> data, string tablename)
        {
            var table = new DataTable
            {
                TableName = tablename
            };
            using (var reader = ObjectReader.Create(data))
            {
                table.Load(reader);
            }

            return table;
        }
        public static DataTable GetDataTableFromListOfDicNew(List<Dictionary<string, object>> list, List<string> colsWithOrdering)
        {
            var dataTable = new DataTable();
            var colToShow = list.FirstOrDefault().Keys.Where(y => colsWithOrdering.Contains(y)).ToList();
            foreach (var columnName in colToShow)
            {
                dataTable.Columns.Add(columnName, typeof(object));
            }

            foreach (var item in list)
            {
                var row = dataTable.NewRow();
                foreach (var keyValuePair in colToShow)
                {
                    row[keyValuePair] = item[keyValuePair];
                }

                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        public static DataTable GetDataTableFromListOfDicNew(IEnumerable<IDictionary<string, object>> source)
        {
            var dataTable = new DataTable();
            if (source.Any())
            {
                var properties = source.First()
                    .Select(p => new DataColumn(p.Key, p.Value?.GetType() ?? typeof(object)))
                    .ToArray();

                dataTable.Columns.AddRange(properties);
                foreach (var item in source)
                {
                    var values = item.Values.Select(value => value ?? DBNull.Value).ToArray();
                    dataTable.Rows.Add(values);
                }
            }

            return dataTable;
        }
    }
}
