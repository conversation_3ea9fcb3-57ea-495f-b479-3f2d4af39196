﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.IDistributor;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.Mapping;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.DistributorRepo
{
    public class DistributorRepository : IDistributorRepository
    {
        private readonly MasterDbContext masterDbContext;
        private readonly DbSet<Distributor> _dbEntity;

        public DistributorRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
            _dbEntity = masterDbContext.Distributors;
        }

        private IQueryable<Distributor> CompanyFilter(long companyId)
        {
            return masterDbContext.Distributors.Where(s => s.CompanyId == companyId);
        }

        private static bool ApplyUsingFADMSFilter(Distributor distributor)
        {
            return distributor.LoginGuid != null;
        }

        private static bool CheckIfIdIsContained(Distributor distributor, List<long> ids)
        {
            return ids.Contains(distributor.Id);
        }

        public async Task<List<DistributorWithGeography>> GetDistributors(long companyId)
        {
            return await CompanyFilter(companyId)
                .Where(s => ApplyUsingFADMSFilter(s))
                .Select(d => DistributorMappingHelper.CreateObject_DistributorWithHierarchy(d))
                .ToListAsync();
        }

        public async Task<DistributorWithGeography> GetDistributorById(long companyId, long distributorId)
        {
            var distributor = await CompanyFilter(companyId)
                .Include(s => s.Region).Include(s => s.Region.Zone)
                .Include(obj => obj.DistributorSegmentations).Include(obj => obj.DistributorChannels)
                .Where(s => s.Id == distributorId)
                .Select(d => DistributorMappingHelper.CreateObject_DistributorWithHierarchy(d))
                .FirstOrDefaultAsync();

            return distributor;
        }

        private async Task<Dictionary<long, T>> GetDistributorData<T>(long companyId, List<long> distributorIds, Func<Distributor, T> selector)
        {
            var distributors = CompanyFilter(companyId)
                .Where(d => ApplyUsingFADMSFilter(d) && CheckIfIdIsContained(d, distributorIds));

            return await distributors.ToDictionaryAsync(d => d.Id, selector);
        }

        public async Task<Dictionary<long, string>> GetDistributorEmailId(long companyId, List<long> distributorIds)
        {
            return await GetDistributorData(companyId, distributorIds, d => d.EmailId);
        }

        public async Task<Dictionary<long, long?>> GetSuperStockistForSubStockist(long companyId, List<long> distributorIds)
        {
            var distributors = CompanyFilter(companyId)
                .Where(d => CheckIfIdIsContained(d, distributorIds) && d.StockistType == StockistType.SubStockist);

            return await distributors.ToDictionaryAsync(d => d.Id, d => d.ParentId);
        }

        public async Task<string> GetWarehouseStateAsync(long companyId, string warehouseErpId)
        {
            return await masterDbContext.DistributorAddresses
                .Where(dist => dist.CompanyId == companyId && dist.WarehouseERPID == warehouseErpId)
                .AsNoTracking()
                .Select(dist => dist.State)
                .FirstOrDefaultAsync();
        }
    }
}
