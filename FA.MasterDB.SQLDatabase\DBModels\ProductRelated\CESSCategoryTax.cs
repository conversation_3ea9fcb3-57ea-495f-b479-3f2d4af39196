﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated;

public class CESSCategoryTax
{
    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public long Id { get; set; }

    public bool IsEnded { get; set; }

    public DateTime? EndedAt { get; set; }

    public decimal CESS { get; set; }

    public ProductCESSCategory CompanyCESSCategory { get; set; }

    public long CompanyCESSCategoryId { get; set; }

    public long CompanyId { get; set; }

    public Company Company { get; set; }
}
