﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.Interfaces;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Route
{
    public class RoutePlanRequest : ICreatedEntity
    {
        public long Id { get; set; }

        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public JourneyFrequency RepeatFrequency { get; set; }

        public bool IsApproved { get; set; }

        public bool Deleted { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public virtual ICollection<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
