﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.PendingPayments
{
    public class FAPendingPaymentInvoice
    {
        public long Id { get; set; }

        public DateTime InvoiceDate { get; set; }

        public DateTime? DueDate { get; set; }

        public string InvoiceId { get; set; }

        public decimal InvoiceAmount { get; set; }

        public decimal? AmountOutstanding { get; set; }

        public int? Aging { get; set; }

        public string Description { get; set; }

        public long FAPendingPaymentId { get; set; }

        public FAPendingPayment FAPendingPayment { get; set; }
    }
}
