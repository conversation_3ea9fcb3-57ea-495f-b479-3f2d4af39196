﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels;
using FA.MasterDB.SQLDatabase.DBModels.Asset;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.Device;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using FA.MasterDB.SQLDatabase.DBModels.Route;
using FA.MasterDB.SQLDatabase.DBModels.Schemes;
using FA.MasterDB.SQLDatabase.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.DBConnection
{
    public class WritableMasterDbContext : Microsoft.EntityFrameworkCore.DbContext
    {
        public WritableMasterDbContext(DbContextOptions<WritableMasterDbContext> options)
            : base(options)
        {
        }

        public DbSet<WritableLocation> F2KLocations { get; set; }

        public DbSet<PinCodeMaster> PinCodeMaster { get; set; }

        public DbSet<OutletUpdationRequest> FAOutletUpdationRequests { get; set; }

        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }

        public DbSet<EmployeeAdvanceLeave> EmployeeAdvanceLeaves { get; set; }

        public DbSet<AdvanceLeaveSubmission> AdvanceLeaveSubmissions { get; set; }

        public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }

        public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }

        public DbSet<RoutePlanRequest> RoutePlanRequests { get; set; }

        public DbSet<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        public DbSet<EmployeeDailyTargets> EmployeeDailyTargets { get; set; }

        public DbSet<EmployeeDailyTargetItems> EmployeeDailyTargetItems { get; set; }

        public DbSet<ManagerAlerts> ManagerAlerts { get; set; }

        public DbSet<DeviceConnection> DeviceConnections { get; set; }

        public DbSet<DeviceToken> DeviceTokens { get; set; }

        public DbSet<Device> Devices { get; set; }

        public DbSet<Device_New> Devices_New { get; set; }

        public DbSet<EmployeeToken> ClientEmployeeTokens { get; set; }

        public DbSet<EmployeeRouteDiversion> EmployeeRouteDiversions { get; set; }

        public DbSet<WritableRouteOutletMapping> RouteOutletMappings { get; set; }

        public DbSet<CueCardsMaster> CueCardsMaster { get; set; }

        public DbSet<OutletMetric> OutletMetric { get; set; }

        public DbSet<OutletwiseExternalMetricValues> OutletwiseExternalMetricValues { get; set; }

        public DbSet<DanoneRunningNumbers> DanoneRunningNumbers { get; set; }

        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }

        public DbSet<Distributor> Distributors { get; set; }

        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }

        public DbSet<ClientEmployee> Employees { get; set; }

        public DbSet<CompanyFactoryStocks> CompanyFactoryStocks { get; set; }

        public DbSet<SchemeBasket> SchemeBuckets { get; set; }

        public DbSet<SchemeSlab> SchemeSlabs { get; set; }

        public DbSet<ProductTagMaster> ProductTagMasters { get; set; }

        public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DeviceToken>().HasIndex(p => p.EmployeeId).IsUnique();

            modelBuilder.Entity<WritableLocation>()
             .Property(sample => sample.Latitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<WritableLocation>()
             .Property(sample => sample.Longitude)
             .HasColumnType("decimal(18,10)");

            modelBuilder.Entity<WritableLocation>().ToTable(t => t.HasTrigger("F2KLocationsAfterUpdate"));
            modelBuilder.Entity<Distributor>().ToTable("FADistributors", t => t.HasTrigger("FADistributorsAfterUpdate"));
        }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("use save changes async instead");
        }

        public override Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default(CancellationToken))
        {
            var newCreatedEntities = ChangeTracker.Entries<ICreatedEntity>()
               .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
            var now = DateTime.UtcNow;
            foreach (var item in newCreatedEntities)
            {
                item.CreatedAt = now;
                item.CreationContext = "From gt app";
            }

            var newDeviceEntities = ChangeTracker.Entries<IDeviceEntity>()
               .Where(e => e.State == EntityState.Added).Select(e => e.Entity).ToList();
            foreach (var item in newDeviceEntities)
            {
                item.ServerTime = now;
            }

            var newUpdatedEntities = ChangeTracker.Entries<IUpdatedEntity>().Where(e => e.State == EntityState.Modified).Select(e => e.Entity).ToList();
            foreach (var item in newUpdatedEntities)
            {
                item.LastUpdatedAt = now;
            }

            return base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            return base.SaveChangesAsync(cancellationToken);
        }
    }
}
