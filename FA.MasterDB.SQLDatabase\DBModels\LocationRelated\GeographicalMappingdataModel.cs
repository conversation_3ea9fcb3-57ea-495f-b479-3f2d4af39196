﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

public class GeographicalMappingdataModel
{
    public long Id { get; set; }

    public string State { get; set; }

    public string District { get; set; }

    public string DistrictCode { get; set; }

    public string SubDistrict { get; set; }

    public string SubDistrictCode { get; set; }

    public string Town { get; set; }

    // public string StateCode { get; set; }
    // public string Zone { get; set; }
    // public string ZoneCode { get; set; }
    // public string TownCode { get; set; }
    // public string TownType { get; set; }
    // public long Population { get; set; }
    // public long Households { get; set; }
    // public long CompanyId { get; set; }
    // public long RegionId { get; set; }
    // public string CityGrade { get; set; }
}
