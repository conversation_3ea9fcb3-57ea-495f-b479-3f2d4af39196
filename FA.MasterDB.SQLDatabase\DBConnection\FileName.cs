﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels;
using FA.MasterDB.SQLDatabase.DBModels.Asset;
using FA.MasterDB.SQLDatabase.DBModels.Beatometer;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.Coupons;
using FA.MasterDB.SQLDatabase.DBModels.Device;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated.FieldAssist.DataAccessLayer.Models.EFModels;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.DBModels.FocusedProduct;
using FA.MasterDB.SQLDatabase.DBModels.Gamification;
using FA.MasterDB.SQLDatabase.DBModels.Journey;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.Margin;
using FA.MasterDB.SQLDatabase.DBModels.OfficalWork;
using FA.MasterDB.SQLDatabase.DBModels.OrderBlock;
using FA.MasterDB.SQLDatabase.DBModels.PendingPayments;
using FA.MasterDB.SQLDatabase.DBModels.Position;
using FA.MasterDB.SQLDatabase.DBModels.PrimarySale;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using FA.MasterDB.SQLDatabase.DBModels.RequestApproval;
using FA.MasterDB.SQLDatabase.DBModels.Route;
using FA.MasterDB.SQLDatabase.DBModels.Schemes;
using FA.MasterDB.SQLDatabase.DBModels.Survey;
using FA.MasterDB.SQLDatabase.DBModels.TaskManagement;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.DBConnection
{
    public class ReadOnlyMasterDbContext : DbContext
    {
        public ReadOnlyMasterDbContext(DbContextOptions<ReadOnlyMasterDbContext> options)
            : base(options)
        {
        }

        // MasterDBModels
        public virtual DbSet<ProductRegionalMeta> ProductRegionalMetas { get; set; }

        public DbSet<IdsLogin> IDSLogins { get; set; }

        public DbSet<CouponsDefinition> CouponsDefinitions { get; set; }

        public virtual DbSet<CouponScheme> CouponSchemes { get; set; }

        public virtual DbSet<CouponSchemeSlab> CouponSchemeSlabs { get; set; }

        public virtual DbSet<CouponMaster> CouponMasters { get; set; }

        public DbSet<SchemeCouponDistribution> SchemeCouponDistributions { get; set; }

        public DbSet<DistributorVanStockNorm> DistributorVanStockNorms { get; set; }

        public DbSet<EmployeeLeave> EmployeeLeaves { get; set; }

        public DbSet<EmployeeToken> ClientEmployeeTokens { get; set; }

        public DbSet<ClientEmployee> Employees { get; set; }

        public DbSet<Designations> Designations { get; set; }

        public DbSet<NewDistributorFieldsAppMeta> NewDistributorFieldsAppMetas { get; set; }

        public DbSet<BeatPlanItem> BeatPlanItems { get; set; }

        public DbSet<NoSalesReasonItem> NoSalesReasonItems { get; set; }

        public DbSet<CompanySetting> CompanySettings { get; set; }

        public DbSet<CompanySettingValue> CompanySettingValues { get; set; }

        public DbSet<LocationBeat> LocationBeats { get; set; }

        public DbSet<RegionWiseODSDB> RegionWiseODS { get; set; }

        public DbSet<GeographicalMapping> GeographicalMappings { get; set; }

        public DbSet<LocationBeatMapping> LocationBeatMappings { get; set; }

        public DbSet<ProductDivision> ProductDivisions { get; set; }

        public DbSet<ProductDisplayCategory> ProductDisplayCategories { get; set; }

        public DbSet<DistributorBeatMappingDB> DistributorBeatMappings { get; set; }

        public DbSet<DistributorRouteMapping> DistributorRouteMapping { get; set; }

        public virtual DbSet<DistributorProductMapping> DistributorProductMappings { get; set; }

        public DbSet<DistributorFieldUserMapping> DistributorFieldUserMappings { get; set; }

        public DbSet<DistributorProductDivisionMappingDB> DistributorProductDivisionMappings { get; set; }

        public DbSet<LocationDB> Locations { get; set; }

        public DbSet<Territory> Territories { get; set; }

        public DbSet<Zone> Zones { get; set; }

        public DbSet<Device> Devices { get; set; }

        public DbSet<Device_New> Devices_New { get; set; }

        public DbSet<BeatPlan> FABeatPlans { get; set; }

        public DbSet<EmployeePjp> EmployeePJPs { get; set; }

        public DbSet<PjpOutletMapping> PJPOutletMappings { get; set; }

        public DbSet<NewOutletFieldsAppMeta> NewOutletFieldsAppMetas { get; set; }

        public DbSet<StateWithDistrict> StateWithDistricts { get; set; }

        public DbSet<ProductSuggestiveQuantity> ProductSuggestiveQuantity { get; set; }

        public DbSet<CompanyDefinedProductType> CompanyDefinedProductTypes { get; set; }

        public DbSet<StockistStock> StockistStocks { get; set; }

        public DbSet<StockistStockItem> StockistStockItems { get; set; }

        public DbSet<PinCodeMaster> PinCodeMaster { get; set; }

        public DbSet<ProductChannelMapping> ProductChannelMappings { get; set; }

        public DbSet<CompanyFactories> CompanyFactories { get; set; }

        public DbSet<DistributorFactoryMappings> DistributorFactoryMappings { get; set; }

        public DbSet<ThemeConfig> ThemeConfig { get; set; }

        // CoreModels
        public DbSet<Company> Companies { get; set; }

        public DbSet<OutletSegmentationAttribute> OutletSegmentationAttributes { get; set; }

        public DbSet<ProductTable> Products { get; set; }

        public DbSet<Survey> Surveys { get; set; }

        public DbSet<Question> Questions { get; set; }

        public DbSet<Choice> Choices { get; set; }

        public DbSet<QuestionGroup> QuestionGroups { get; set; }

        public DbSet<SurveyToCompanyZoneMap> SurveyToCompanyZoneMaps { get; set; }

        public DbSet<ProductPrimaryCategory> ProductPrimaryCategories { get; set; }

        public DbSet<CompanyAppMeta> CompanyAppMetas { get; set; }

        public DbSet<EmployeeProductDivisionMapping> EmployeeProductDivisionMappings { get; set; }

        public DbSet<NoSalesReason> NoSalesReasons { get; set; }

        public DbSet<CountryInfo> CountryDetails { get; set; }

        public DbSet<Region> Regions { get; set; }

        public DbSet<Distributor> Distributors { get; set; }

        public DbSet<EmployeeDayStartRecord> EmployeeDayStartRecords { get; set; }

        public DbSet<ProductSecondaryCategory> ProductSecondaryCategories { get; set; }

        public DbSet<FAEmployeeBeatMappings> EmployeeBeatMappings { get; set; }

        public DbSet<EmployeeRouteMapping> EmployeeRouteMappings { get; set; }

        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMappings { get; set; }

        public DbSet<CompanyNomenclature> CompanyNomenclatures { get; set; }

        public DbSet<ZonalProductMeta> ZonalProductMetas { get; set; }

        public DbSet<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }

        public DbSet<EmployeeTourPlanItemSecondary> EmployeeTourPlanItemsSecondary { get; set; }

        public DbSet<EmployeeTourPlan> EmployeeTourPlans { get; set; }

        public DbSet<EmployeeDailyTargets> EmployeeDailyTargets { get; set; }

        public DbSet<EmployeeDailyTargetItems> EmployeeDailyTargetItems { get; set; }

        public DbSet<RoutePlan> RoutePlans { get; set; }

        public DbSet<RoutePlanItem> RoutePlanItems { get; set; }

        public DbSet<SecondaryRoutePlanItem> SecondaryRoutePlanItems { get; set; }

        public DbSet<RoutePlanRequest> RoutePlanRequests { get; set; }

        public DbSet<RoutePlanRequestItem> RoutePlanRequestItems { get; set; }

        public DbSet<RouteClass> Routes { get; set; }

        public DbSet<RouteOutletMapping> RouteOutletMappings { get; set; }

        public DbSet<Scheme> Schemes { get; set; }

        public DbSet<CompanyAdmin> CompanyAdmins { get; set; }

        public DbSet<InternationalDiscount> InternationalDiscounts { get; set; }

        public DbSet<SchemeSlab> SchemeSlabs { get; set; }

        public DbSet<QualifierSchemeSlab> QualifierSchemeSlabs { get; set; }

        public DbSet<SchemeBasket> SchemeBuckets { get; set; }

        public DbSet<OfficialWorkTypeHierarchyMapping> OfficialWorkTypeHierarchyMappings { get; set; }

        public DbSet<OfficialWorkTypes> OfficialWorkTypes { get; set; }

        public DbSet<ShopType> ShopTypes { get; set; }

        public DbSet<Channel> Channels { get; set; }

        public DbSet<MarginSlab> MarginSlab { get; set; }

        public DbSet<EntityMarginSlab> EntityMarginSlabs { get; set; }

        public DbSet<FAPendingPayment> FAPendingPayments { get; set; }

        public DbSet<FAPendingPaymentInvoice> FAPendingPaymentInvoices { get; set; }

        public DbSet<Team> Teams { get; set; }

        public DbSet<TeamUserMapping> TeamUserMappings { get; set; }

        public DbSet<KPI> KPIs { get; set; }

        public DbSet<CompanyKPI> CompanyKPIs { get; set; }

        public DbSet<Game> Games { get; set; }

        public DbSet<GameAlert> GameAlert { get; set; }

        public DbSet<TargetForTeams> TargetForTeams { get; set; }

        public DbSet<CoinsforKpi> CoinsforKpis { get; set; }

        public DbSet<AssetDefinition> AssetDefinitions { get; set; }

        public DbSet<AssetOutletMapping> AssetOutletMappings { get; set; }

        public DbSet<QuantityPurchaseScheme> QuantityPurchaseSchemes { get; set; }

        public DbSet<ExternalAsset> ExternalAssets { get; set; }

        public DbSet<SMSConfiguration> SMSConfigurations { get; set; }

        public DbSet<GSTCategoryTax> GSTCategoryTaxes { get; set; }

        public DbSet<ProductGSTCategory> ProductGSTCategories { get; set; }

        public DbSet<KPISlab> KPISlabs { get; set; }

        public DbSet<VanDistributorMapping> VanDistributorMapping { get; set; }

        public DbSet<VanMaster> VanMaster { get; set; }

        public DbSet<PositionCodeEntityMapping> PositionCodeEntityMappings { get; set; }

        public DbSet<PositionCodeHierarchy> PositionCodeHierarchies { get; set; }

        public DbSet<PositionCode> PositionCodes { get; set; }

        public DbSet<PositionBeatMapping> PositionBeatMapping { get; set; }

        public DbSet<PositionProductDivisionMapping> PositionProductDivisionMappings { get; set; }

        public DbSet<RoutePositionMapping> RoutePositionMapping { get; set; }

        public DbSet<PositionDistributorMapping> PositionDistributorMappings { get; set; }

        public DbSet<ProductPricingMasters> ProductPricingMasters { get; set; }

        public DbSet<ExternalApiToken> ExternalApiTokens { get; set; }

        public DbSet<CarouselBanners> CarouselBanners { get; set; }

        public DbSet<PrimarySale> PrimarySales { get; set; }

        public DbSet<PrimarySaleItems> PrimarySaleItems { get; set; }

        public DbSet<DistributorWiseOutletCreditLimit> DistributorWiseOutletCreditLimit { get; set; }

        public virtual DbSet<BilledtoshippedtoAddress> BilledtoshippedtoAddress { get; set; }

        public DbSet<DanoneRunningNumbers> DanoneRunningNumbers { get; set; }

        public DbSet<OutletTag> OutletTags { get; set; }

        public DbSet<DistributorAddress> DistributorAddresses { get; set; }

        public DbSet<ProductCESSCategory> ProductCESSCategories { get; set; }

        public DbSet<CESSCategoryTax> CESSCategoryTaxes { get; set; }

        public DbSet<JourneyCalendar> JourneyCalendars { get; set; }

        public DbSet<JourneyCycle> JourneyCycles { get; set; }

        public DbSet<JourneyWeek> JourneyWeeks { get; set; }

        public DbSet<JourneyPlanConfigurations> JourneyPlanConfigurations { get; set; }

        public DbSet<OutletMetric> OutletMetrices { get; set; }

        public DbSet<OutletwiseExternalMetricValues> OutletwiseExternalMetricValues { get; set; }

        public DbSet<GlobalOutletMetrices> GlobalOutletMetrices { get; set; }

        public DbSet<FAProductBasket> ProductBasket { get; set; }

        public DbSet<FABasketProductMappings> BasketProductMappings { get; set; }

        public DbSet<CompanyNomenclature> CompanyNomenclature { get; set; }

        public DbSet<CompanyNomenclatureMapping> CompanyNomenclatureMapping { get; set; }

        public DbSet<FAProductSet> FAProductSet { get; set; }

        public DbSet<DistributorToRetailerMargins> DistributorToRetailerMargins { get; set; }

        public DbSet<CompanyTargetSubscriptions> CompanyTargetSubscriptions { get; set; }

        public DbSet<CompanyTargets> CompanyTargets { get; set; }

        public DbSet<BeatometerRuleDetails> BeatometerRuleDetails { get; set; }

        public DbSet<TargetMaster> TargetMaster { get; set; }

        public DbSet<FocusedProductRule> FocusedProductRules { get; set; }

        public DbSet<FocusedProductRulePositionCodeMapping> FocusedProductRulePositionCodeMappings { get; set; }

        public DbSet<CueCardsMaster> CueCardsMasters { get; set; }

        public DbSet<TaskManagementFocusArea> TaskManagementFocusAreas { get; set; }

        public DbSet<TaskManagementUserFocusArea> TaskManagementUserFocusAreas { get; set; }

        public DbSet<TaskManagementTask> TaskManagementTasks { get; set; }

        public DbSet<CompanyExternalMetrices> CompanyExternalMetrices { get; set; }

        public DbSet<EquipmentMaster> EquipmentMaster { get; set; }

        public DbSet<Reward> FARewards { get; set; }

        public DbSet<ImageRecognitionLogic> ImageRecognitionLogics { get; set; }

        public DbSet<ExternalLoginAccess> ExternalLoginAccesses { get; set; }

        public DbSet<UserWiseSchemeBudget> UserWiseSchemeBudget { get; set; }

        public DbSet<OrderBlock> OrderBlock { get; set; }

        public DbSet<OrderBlockItems> OrderBlockItems { get; set; }

        public DbSet<OutletUpdationRequest> FAOutletUpdationRequests { get; set; }

        public DbSet<EmployeeAdvanceLeave> EmployeeAdvanceLeaves { get; set; }

        public DbSet<AdvanceLeaveSubmission> AdvanceLeaveSubmissions { get; set; }

        public DbSet<DeviceConnection> DeviceConnections { get; set; }

        public DbSet<DeviceToken> DeviceTokens { get; set; }

        public DbSet<BiReportSubscriptions> BiReportSubscriptions { get; set; }

        public DbSet<PerfectStoreRule> PerfectStoreRules { get; set; }

        public DbSet<CompanyFactoryStocks> CompanyFactoryStocks { get; set; }

        public DbSet<ProductTagMaster> ProductTagMasters { get; set; }

        public DbSet<ProductTagSuggestion> ProductTagSuggestions { get; set; }

        public DbSet<DynamicCarouselBanner> DynamicCarouselBanners { get; set; }

        public DbSet<Cohort> Cohorts { get; set; }

        public DbSet<OutletOnboardingDetails> OutletOnboardingDetails { get; set; }

        public DbSet<DistributorSegmentations> DistributorSegmentations { get; set; }

        public DbSet<DistributorChannels> DistributorChannels { get; set; }

        public DbSet<RequestApprovalTimeline> RequestApprovalTimeline { get; set; }

        public DbSet<RequestApprovalRules> RequestApprovalRules { get; set; }

        // Employee-related entities
        public DbSet<ClientEmployee> ClientEmployees { get; set; }
        public DbSet<FAEmployeeBeatMappings> FAEmployeeBeatMappings { get; set; }

        //Company entities
        public DbSet<CompanyFactory> CompanyFactory { get; set; }

        //Scheme
        public DbSet<Scheme> Scheme { get; set; }

        // Other entities
        public DbSet<DbExtApiToken> DbExtApiToken { get; set; }
        public DbSet<ClaimsConfiguration> ClaimsConfiguration { get; set; }
        public DbSet<DMSPOApprovalRights> DMSPOApprovalRights { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<DistributorWiseOutletCreditLimit>().ToTable("DistributorWiseOutletCreditLimit");
            modelBuilder.Entity<CompanyAppMeta>().ToTable("FACompaniesAppMeta");
            modelBuilder.Entity<CompanyNomenclatureMapping>().ToTable("FACompanyNomenclaturesMapping");
            modelBuilder.Entity<CompanyNomenclature>().ToTable("FACompanyNomenclatures");
            modelBuilder.Entity<FAEmployeeBeatMappings>().ToTable("FAEmployeeBeatMappings");
            modelBuilder.Entity<EmployeeRouteMapping>().ToTable("FAEmployeeRouteMappings");
            modelBuilder.Entity<Distributor>().ToTable(
                "FADistributors",
                t => t.HasTrigger("FADistributorsAfterUpdate"));
            modelBuilder.Entity<LocationDB>().ToTable("F2KLocations");
            modelBuilder.Entity<RegionWiseODSDB>().ToTable("RegionWiseODS");
            modelBuilder.Entity<DistributorToRetailerMargins>().ToTable("DistributorToRetailerMargins");

            // for product
            modelBuilder.Entity<ProductTable>().ToTable("FACompanyProducts");
            modelBuilder.Entity<ProductTable>().Property(b => b.DisplayMRP).HasColumnName("MRP");
            modelBuilder.Entity<FAProductBasket>().ToTable("FAProductBasket");
            modelBuilder.Entity<FABasketProductMappings>().ToTable("FABasketProductMappings");
            modelBuilder.Entity<FAProductSet>().ToTable("FAProductSet");

            // managers
            modelBuilder.Entity<ProductPrimaryCategory>().ToTable("FAProductPrimaryCategory");
            modelBuilder.Entity<ProductSecondaryCategory>().ToTable("FAProductSecondaryCategory");
            modelBuilder.Entity<ProductDivision>().ToTable("FAProductDivision");

            modelBuilder.Entity<SurveyToCompanyZoneMap>().ToTable("FASurveyToCompanyZoneMaps");
            modelBuilder.Entity<Survey>().ToTable("NewSurvey_JsonForm");
            modelBuilder.Entity<QuestionGroup>().ToTable("NewSurvey_QuestionGroup");
            modelBuilder.Entity<Question>().ToTable("NewSurvey_Question");
            modelBuilder.Entity<Choice>().ToTable("NewSurvey_QuestionChoice");

            modelBuilder.Entity<CountryInfo>().HasKey(b => b.CountryName);

            // Ignore Outlets from mapping.
            modelBuilder.Entity<Survey>().Ignore(b => b.Outlets);

            // Task-Management
            modelBuilder.Entity<TaskManagementUserFocusArea>()
                .HasOne(ufa => ufa.TaskManagementFocusAreas)
                .WithOne(fa => fa.TaskManagementUserFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementFocusArea>()
                .HasOne(fa => fa.TaskManagementUserFocusAreas)
                .WithOne(ufa => ufa.TaskManagementFocusAreas)
                .HasForeignKey<TaskManagementUserFocusArea>(ufa => ufa.TaskManagementFocusAreaID);

            modelBuilder.Entity<TaskManagementTask>()
                .HasOne(tt => tt.TaskManagementFocusAreas)
                .WithMany(fa => fa.TaskManagementTasks)
                .HasForeignKey(tt => tt.TaskManagementFocusAreaID);

            modelBuilder.Entity<FocusedProductRule>().ToTable("FAFocusedProductRule");
        }

        public override int SaveChanges()
        {
            throw new InvalidOperationException("The context is readonly");
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            throw new InvalidOperationException("The context is readonly");
        }
    }
}
