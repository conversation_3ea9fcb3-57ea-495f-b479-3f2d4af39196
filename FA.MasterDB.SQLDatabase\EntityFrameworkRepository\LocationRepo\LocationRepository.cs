﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.Mapping;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo
{
    public class LocationRepository : ILocationRepository
    {
        private readonly MasterDbContext masterDbContext;

        public LocationRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        private IQueryable<Location> CompanyFilter(
            long companyId)
        {
            return masterDbContext.Locations.Where(s => s.Company
                == companyId);
        }

        public async Task<LocationWithGeoHierarchy> GetOutletById(long companyId, long id)
        {
            var outlet = await CompanyFilter(companyId).
                Include(s => s.Beat).ThenInclude(s => s.Territory)
                .ThenInclude(s => s.Region).ThenInclude(s => s.Zone)
                .Include(s => s.ShopType).ThenInclude(s => s.Channel).
                Where(s => s.Id == id).Select(l =>
                    LocationMapping.
                    CreateObject_LocationWithHierarchy(l)
                ).FirstOrDefaultAsync();
            if (outlet == null)
            {
                throw new Exception($"Outlet cannot be null. ID: {id}");

            }

            return outlet;
        }
    }
}
