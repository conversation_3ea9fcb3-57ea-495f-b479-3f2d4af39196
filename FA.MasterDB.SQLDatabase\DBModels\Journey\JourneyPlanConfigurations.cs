﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Journey;

public class JourneyPlanConfigurations
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public JourneyCreationSettingType? JourneyCreationSettingType { get; set; }

    public PositionLevelNomenclature? PositionLevel { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }
}
