﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{

    public class SMSConfiguration
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }
        public OrderKind OrderKind { get; set; }

        public string CompanyName { get; set; }
        public string Url { get; set; }
        public bool SendOnlyProductive { get; set; }
        public string Message { get; set; }
        public string UnProductiveMessage { get; set; }
        public string OTPMessage { get; set; }
        public bool IsPOSTOnly { get; set; }

        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsDeactive { get; set; }

        public SMSConfiguration ToLiberaryModel()
        {
            return new SMSConfiguration
            {
                CompanyId = CompanyId,
                CompanyName = CompanyName,
                CreatedAt = CreatedAt,
                CreationContext = CreationContext,
                Id = Id,
                IsDeactive = IsDeactive,
                IsPOSTOnly = IsPOSTOnly,
                LastUpdatedAt = LastUpdatedAt,
                Message = Message,
                OrderKind = OrderKind,
                OTPMessage = OTPMessage,
                SendOnlyProductive = SendOnlyProductive,
                UnProductiveMessage = UnProductiveMessage,
                Url = Url
            };
        }
    }
}
