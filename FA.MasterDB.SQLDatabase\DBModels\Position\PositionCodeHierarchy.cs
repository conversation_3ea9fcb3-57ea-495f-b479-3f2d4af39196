﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Position
{
    public class PositionCodeHierarchy
    {
        public long? PositionLevel1 { get; set; }

        public DateTime CreatedAt { get; set; }

        public long CompanyId { get; set; }

        public long? PositionLevel2 { get; set; }

        public long? PositionLevel3 { get; set; }

        public long? PositionLevel4 { get; set; }

        public long? PositionLevel5 { get; set; }

        public long? PositionLevel6 { get; set; }

        public long? PositionLevel7 { get; set; }

        public long? PositionLevel8 { get; set; }

        public long? ZoneId { get; set; }

        public long? RegionId { get; set; }

        public long? GeographyLevel5 { get; set; }

        public long? GeographyLevel6 { get; set; }

        public long? GeographyLevel7 { get; set; }

        public long Id { get; set; }

        public PositionLevelNomenclature? PositionCodeLevel { get; set; }

        public long? PositionLevel1UserId { get; set; }

        public long? PositionLevel2UserId { get; set; }

        public long? PositionLevel3UserId { get; set; }

        public long? PositionLevel4UserId { get; set; }

        public long? PositionLevel5UserId { get; set; }

        public long? PositionLevel6UserId { get; set; }

        public long? PositionLevel7UserId { get; set; }

        public long? PositionLevel8UserId { get; set; }

        public PositionCodeHierarchy UpdateUsingPositionLevel()
        {
            switch (PositionCodeLevel)
            {
                case PositionLevelNomenclature.L8Position:
                    PositionLevel8UserId = PositionLevel1UserId;
                    PositionLevel7UserId = PositionLevel1UserId;
                    PositionLevel6UserId = PositionLevel1UserId;
                    PositionLevel5UserId = PositionLevel1UserId;
                    PositionLevel4UserId = PositionLevel1UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel1;
                    PositionLevel7 = PositionLevel1;
                    PositionLevel6 = PositionLevel1;
                    PositionLevel5 = PositionLevel1;
                    PositionLevel4 = PositionLevel1;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L7Position:
                    PositionLevel8UserId = PositionLevel2UserId;
                    PositionLevel7UserId = PositionLevel1UserId;
                    PositionLevel6UserId = PositionLevel1UserId;
                    PositionLevel5UserId = PositionLevel1UserId;
                    PositionLevel4UserId = PositionLevel1UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel2;
                    PositionLevel7 = PositionLevel1;
                    PositionLevel6 = PositionLevel1;
                    PositionLevel5 = PositionLevel1;
                    PositionLevel4 = PositionLevel1;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L6Position:
                    PositionLevel8UserId = PositionLevel3UserId;
                    PositionLevel7UserId = PositionLevel2UserId;
                    PositionLevel6UserId = PositionLevel1UserId;
                    PositionLevel5UserId = PositionLevel1UserId;
                    PositionLevel4UserId = PositionLevel1UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel3;
                    PositionLevel7 = PositionLevel2;
                    PositionLevel6 = PositionLevel1;
                    PositionLevel5 = PositionLevel1;
                    PositionLevel4 = PositionLevel1;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L5Position:
                    PositionLevel8UserId = PositionLevel4UserId;
                    PositionLevel7UserId = PositionLevel3UserId;
                    PositionLevel6UserId = PositionLevel2UserId;
                    PositionLevel5UserId = PositionLevel1UserId;
                    PositionLevel4UserId = PositionLevel1UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel4;
                    PositionLevel7 = PositionLevel3;
                    PositionLevel6 = PositionLevel2;
                    PositionLevel5 = PositionLevel1;
                    PositionLevel4 = PositionLevel1;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L4Position:
                    PositionLevel8UserId = PositionLevel5UserId;
                    PositionLevel7UserId = PositionLevel4UserId;
                    PositionLevel6UserId = PositionLevel3UserId;
                    PositionLevel5UserId = PositionLevel2UserId;
                    PositionLevel4UserId = PositionLevel1UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel5;
                    PositionLevel7 = PositionLevel4;
                    PositionLevel6 = PositionLevel3;
                    PositionLevel5 = PositionLevel2;
                    PositionLevel4 = PositionLevel1;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L3Position:
                    PositionLevel8UserId = PositionLevel6UserId;
                    PositionLevel7UserId = PositionLevel5UserId;
                    PositionLevel6UserId = PositionLevel4UserId;
                    PositionLevel5UserId = PositionLevel3UserId;
                    PositionLevel4UserId = PositionLevel2UserId;
                    PositionLevel3UserId = PositionLevel1UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel6;
                    PositionLevel7 = PositionLevel5;
                    PositionLevel6 = PositionLevel4;
                    PositionLevel5 = PositionLevel3;
                    PositionLevel4 = PositionLevel2;
                    PositionLevel3 = PositionLevel1;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;

                case PositionLevelNomenclature.L2Position:
                    PositionLevel8UserId = PositionLevel7UserId;
                    PositionLevel7UserId = PositionLevel6UserId;
                    PositionLevel6UserId = PositionLevel5UserId;
                    PositionLevel5UserId = PositionLevel4UserId;
                    PositionLevel4UserId = PositionLevel3UserId;
                    PositionLevel3UserId = PositionLevel2UserId;
                    PositionLevel2UserId = PositionLevel1UserId;
                    PositionLevel1UserId = PositionLevel1UserId;
                    PositionLevel8 = PositionLevel7;
                    PositionLevel7 = PositionLevel6;
                    PositionLevel6 = PositionLevel5;
                    PositionLevel5 = PositionLevel4;
                    PositionLevel4 = PositionLevel3;
                    PositionLevel3 = PositionLevel2;
                    PositionLevel2 = PositionLevel1;
                    PositionLevel1 = PositionLevel1;
                    break;
            }

            return this;
        }
    }
}
