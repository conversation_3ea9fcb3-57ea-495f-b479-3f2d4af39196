﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Gamification
{
    [Table("Games")]
    public class Game
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public GameRewardType RewardType { get; set; }

        public long CompanyId { get; set; }

        public bool IsActive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public List<CoinsforKpi> CoinsforKpi { get; set; }

        public List<TargetForTeams> TargetsforTeams { get; set; }
    }
}
