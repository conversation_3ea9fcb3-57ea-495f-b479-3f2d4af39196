﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    [Table("ClientEmployees")]
    public class ClientEmployee
    {
        [Key]
        public long Id { get; set; }
        public Guid GUID { get; set; }
        public string Name { get; set; }
        [Column("Deleted")]
        public bool IsDeactive { get; set; }
        public long Company { get; set; }
        public string? ClientSideId { get; set; }
        public long? ParentId { get; set; }
        public PortalUserRole UserRole { set; get; }
        public EmployeeRank Rank { get; set; }
        public long? OldTableId { get; set; }
        public string? LocalName { get; set; }
        public string? ContactNo { get; set; }
        public ClientEmployee? Parent { get; set; }
        public long? RegionId { get; set; }
        public bool IsFieldAppuser { get; set; }
        public DateTime? DateOfJoining { set; get; }
        public Region? Region { get; set; }
        public bool IsTrainingUser { get; set; }
        public bool IsOrderBookingDisabled { get; set; }
        public string? EmailId { get; set; }
        public long? KRATagId { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public EmployeeType UserType { get; set; }
        public long? DesignationId { get; set; }
        public string? UserProfilePicture { get; set; }
        public long? AreaSalesManagerId { get; set; }
        public bool isVacant { get; set; }

        public string IMEINumber { get; set; }

        public bool IsDeleted { get; set; }

        public bool? SendLatestApp { get; set; }

        public string Zone { get; set; }

        public bool IsFieldAppUser { get; set; }

        public bool IsVacant { get; set; }

        public JourneyType? JourneyPlanType { get; set; }

        public bool? IsGeofencingActive { get; set; }

        public string EmployeeAttributeText2 { get; set; }

        public double? EmployeeAttributeNumber2 { get; set; }

        public double? EmployeeAttributeNumber1 { get; set; }

        public double? Salary { get; set; }

        public bool? EmployeeAttributeBoolean2 { get; set; }

        public bool? EmployeeAttributeBoolean1 { get; set; }

        public string EmployeeAttributeText1 { get; set; }

        public DateTime? EmployeeAttributeDate { get; set; }

        public string Address { get; set; }

        public string PinCode { get; set; }

        public string City { get; set; }

        public DateTime? DateOfBirth { get; set; }

        public MaritalStatusEnum? MaritalStatus { get; set; }

        public DateTime? DateOfAnniversary { get; set; }

        [Column("IsRural")]
        public bool IsRural { get; set; }

        [Column("JourneyPlanEntity")]
        public UserJourneyPlanningEntity JourneyPlanEntity { get; set; }
    }
}
