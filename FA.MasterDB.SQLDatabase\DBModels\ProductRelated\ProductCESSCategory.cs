﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    public class ProductCESSCategory
    {
        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public long Id { get; set; }

        public bool Deleted { get; set; }

        [StringLength(256)]
        public string Name { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public IEnumerable<CESSCategoryTax> CESSCategoryTaxes { get; set; }
    }
}
