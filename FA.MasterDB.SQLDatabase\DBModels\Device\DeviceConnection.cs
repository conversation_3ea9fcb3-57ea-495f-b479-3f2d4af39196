﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Device
{
    public class DeviceConnection
    {
        public long ID { get; set; }

        public long EmployeeID { get; set; }

        public DateTime AddedOn { get; set; }

        public virtual ClientEmployee Employee { get; set; }

        public bool? IsInValid { get; set; }

        public bool IsValid()
        {
            // Assumed that AddedOn is UTC time
            // The Token expires in 60 minutes
            const int expiresInMinutes = 60; // temporary jugad for Foreign client training
            return (IsInValid == null || !IsInValid.Value) && (DateTime.UtcNow - AddedOn).TotalMinutes < expiresInMinutes;
        }
    }
}
