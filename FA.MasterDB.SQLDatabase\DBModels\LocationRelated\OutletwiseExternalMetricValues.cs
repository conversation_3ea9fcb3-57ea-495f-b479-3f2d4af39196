﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("OutletwiseExternalMetricValues")]
    public class OutletwiseExternalMetricValues
    {
        public long Id { get; set; }

        public long EntityId { get; set; }

        public long MetricId { get; set; }

        [StringLength(1000)]
        public string MetricValue { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public OutletMetric Metric { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }
    }
}
