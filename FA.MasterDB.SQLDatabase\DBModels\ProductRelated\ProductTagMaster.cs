﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("ProductTagMasters")]
    public class ProductTagMaster
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("Name")]
        [StringLength(64)]
        public string Name { get; set; }

        [Column("RecommendationTag")]
        public RecommendationTag RecommendationTag { get; set; }

        [Column("ERPId")]
        [StringLength(16)]
        public string ErpId { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("CreationContext")]
        [StringLength(32)]
        public string CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }
    }
}
