﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("DynamicCarouselBanners")]
    public class DynamicCarouselBanner
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("Cohort")]
        public string Cohort { get; set; }

        [Column("StartDate")]
        public DateTime? StartDate { get; set; }

        [Column("EndDate")]
        public DateTime? EndDate { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }
    }
}
