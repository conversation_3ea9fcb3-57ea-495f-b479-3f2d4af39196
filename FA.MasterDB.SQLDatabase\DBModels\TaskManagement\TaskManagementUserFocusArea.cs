﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.TaskManagement
{
    [Table("TaskManagementUserFocusAreas")]
    public class TaskManagementUserFocusArea
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("TaskManagementFocusAreaID")]
        public long? TaskManagementFocusAreaID { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("TargetQuery")]
        public string TargetQuery { get; set; }

        [Column("AchievementQuery")]
        public string AchievementQuery { get; set; }

        [Column("EntityId")]
        public long? EntityId { get; set; }

        [Column("Description")]
        public string Description { get; set; }

        [Column("IsDeactive")]
        public bool? IsDeactive { get; set; }

        [Column("CreatedAt")]
        public DateTime? CreatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime? LastUpdatedAt { get; set; }

        [Column("TargetUIName")]
        public string TargetUIName { get; set; }

        [Column("TargetQueryDB")]
        public TaskManagementQueryRelation TargetQueryDB { get; set; }

        [Column("AchievementUIName")]
        public string AchievementUIName { get; set; }

        [Column("AchievementQueryDB")]
        public TaskManagementQueryRelation AchievementQueryDB { get; set; }

        [Column("Instruction")]
        public string Instruction { get; set; }

        [Column("TaskTitle")]
        public string TaskTitle { get; set; }

        [Column("UIElementType")]
        public UIElementType UIElementType { get; set; }

        [ForeignKey("TaskManagementFocusAreaID")]
        public TaskManagementFocusArea TaskManagementFocusAreas { get; set; }
    }
}
