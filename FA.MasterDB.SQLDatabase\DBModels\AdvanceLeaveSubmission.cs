﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.Interfaces;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels;

public class AdvanceLeaveSubmission : ICreatedEntity
{
    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public long Id { get; set; }

    public bool Deleted { get; set; }

    public long EmployeeId { get; set; }

    public string PositionCodesId { get; set; }

    public long? ManagerId { get; set; }

    public int NumberOfDays { get; set; }

    public int NumberOfLeavesApproved { get; set; }

    public long CompanyId { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public ActionOnLeave ActionOnLeave { get; set; }

    [StringLength(1024)]
    public string Reason { get; set; }

    public virtual Company Company { get; set; }

    public virtual ClientEmployee Employee { get; set; }

    public string ImageURL { get; set; }
}
