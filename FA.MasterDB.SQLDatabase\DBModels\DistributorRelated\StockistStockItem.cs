﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;

public class StockistStockItem
{
    public long Id { get; set; }

    public long ProductId { get; set; }

    public int StockValue { get; set; }

    public long DistributorStockId { get; set; }

    [Column("ExpiryDate")]
    public DateTime? ExpiryDate { get; set; }

    [Column("Unit")]
    public string Unit { get; set; }

    [Column("Batch")]
    public string Batch { get; set; }

    public virtual StockistStock DistributorStock { get; set; }
}
