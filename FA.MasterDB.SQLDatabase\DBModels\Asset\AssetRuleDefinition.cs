﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.Asset;

public class AssetRuleDefinition
{
    public long Id { get; set; }
    public long CompanyId { get; set; }
    public bool IsActive { get; set; }
    public long HighLimit { get; set; }
    public long MediumLimit { get; set; }
    public string CreationContext { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime UpdatedOn { get; set; }
}
