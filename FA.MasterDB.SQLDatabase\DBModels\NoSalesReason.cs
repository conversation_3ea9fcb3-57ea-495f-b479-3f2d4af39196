﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("FANoSalesReasons")]
    public class NoSalesReason
    {
        public long Id { get; set; }

        public string Reason { get; set; }

        public string ChildStatement { get; set; }

        public long CompanyId { get; set; }

        public DateTime AddedOn { get; set; }
    }
}
