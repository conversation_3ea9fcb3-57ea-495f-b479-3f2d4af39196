﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class DistributorWiseOutletCreditLimit
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long DistributorId { get; set; }

        public string DistributorErpId { get; set; }

        public long OutletId { get; set; }

        public string OutletErpId { get; set; }

        public int? CreditDuration { get; set; }

        public double? CreditValue { get; set; }

        public int? CreditInvoiceCount { get; set; }

        public bool IsActive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
