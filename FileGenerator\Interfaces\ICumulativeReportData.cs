﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using FileGenerator.Attributes;

namespace FileGenerator.Interfaces
{
    public interface ICumulativeReportData
    {
        object Value { get; set; }
        DataTypeAttribute DataType { get; set; }
        List<string> Headers { get; set; }
        string ParentHeader { get; set; }
        string HeaderKey { get; set; }
        string CellFormat { get; }
    }
}
