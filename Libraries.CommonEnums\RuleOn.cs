﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum RuleOn
{
    [Display(Name = "Productive Call Basis")]
    ProductiveCallBasis = 0,
    [Display(Name = "Visit Basis")]
    VisitBasis = 1
}

public enum RuleType
{
    [Display(Name = "Perfect Store")]
    PerfectStore = 0,
    [Display(Name = "Perfect Call")]
    PerfectCall = 1
}
public enum SubRuleType
{
    [Display(Name = "Incentive")]
    Incentive = 0,
    [Display(Name = "Loyalty")]
    Loyalty = 1,
    [Display(Name = "Appraisal")]
    Appraisal = 2,
}
