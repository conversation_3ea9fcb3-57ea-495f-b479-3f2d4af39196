﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Asset
{
    [Table("AssetOutletMappings")]
    public class AssetOutletMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        [ForeignKey("AssetDefinition")]
        public long AssetDefinitionsId { get; set; }

        public long LocationId { get; set; }

        public string AssetReferenceNo { get; set; }

        public string AssetState { get; set; }

        public string ImageId { get; set; }

        public string CreationContext { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public bool isDeleted { get; set; }

        [ForeignKey("EquipmentMaster")]
        public long? EquipmentId { get; set; }

        public virtual EquipmentMaster EquipmentMaster { get; set; }

        public virtual AssetDefinition AssetDefinition { get; set; }
    }
}

