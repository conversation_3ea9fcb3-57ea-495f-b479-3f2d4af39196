﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class DMSPOApprovalRights
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public long DistributorId { get; set; }
        public PortalUserRole UserRole { get; set; }
        public bool CanApprove { get; set; }
        public bool CanEdit { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public bool Deleted { get; set; }
        public bool IsDeactive { get; set; }
    }
}
