﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("FACompanyZones")]
    public class Zone
    {
        public long Id { get; set; }
        public string ErpId { get; set; }
        public string Name { get; set; }
        public long Company { get; set; }
        public bool IsDeactive { get; set; }
    }
}
