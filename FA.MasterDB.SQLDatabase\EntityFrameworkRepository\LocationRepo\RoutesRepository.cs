﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.SQLDatabase.DBConnection;
using Libraries.CommonModels;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo
{

    public class RoutesRepository : IRoutesRepository
    {
        private readonly MasterDbContext masterDbContext;
        public RoutesRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        public async Task<EntityMin> GetMinAsync(long routeId, long companyId)
        {
            return await masterDbContext.Routes.Where(s => s.Id == routeId && s.CompanyId == companyId)
                .Select(r => new EntityMin()
                {
                    Id = r.Id,
                    Name = r.Name
                }).FirstOrDefaultAsync()
                ?? new EntityMin();
        }
    }
}
