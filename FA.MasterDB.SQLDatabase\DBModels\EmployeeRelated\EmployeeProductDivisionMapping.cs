﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class EmployeeProductDivisionMapping
    {
        public EmployeeProductDivisionMapping()
        {
            Deleted = false;
        }

        public long Id { get; set; }

        public long EmployeeId { get; set; }

        public bool Deleted { get; set; }

        public long ProductDivisionId { get; set; }

        // public DateTime CreatedAt { get; set; }
        public long CompanyId { get; set; }

        // public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
