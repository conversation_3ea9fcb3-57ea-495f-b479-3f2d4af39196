﻿// Copyright (c) FieldAssist. All Rights Reserved.

//using System.Data.Entity;
using FA.MasterDB.Core.Repositories.IProduct;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.ProductRepo
{
    public class ProductDisplayCategoriesRepository :
        IProductDisplayCategoriesRepository
    {
        private readonly MasterDbContext masterDbContext;

        public ProductDisplayCategoriesRepository(
            MasterDbContext sQLMasterDbContext)
        {
            masterDbContext = sQLMasterDbContext;
        }

        private IQueryable<ProductDisplayCategories> CompanyFilter(
            long companyId)
        {
            return masterDbContext.ProductDisplayCategories.Where(s => s.CompanyId
                == companyId);
        }

        public async Task<Dictionary<long, string>> GetDisplayCategoryNameWithId
            (List<long> DisplayCategoryIds, long CompanyId)
        {
            return await CompanyFilter(CompanyId)
                .Where(p => DisplayCategoryIds.Contains(p.Id))
                .ToDictionaryAsync(p => p.Id, s => s.Name);
        }
    }
}
