﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class GlobalOutletMetrices
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public bool IsDeleted { get; set; }

        public string TransactionSqlQuery { get; set; }

        public string ReportSqlQuery { get; set; }

        public string MasterSqlQuery { get; set; }

        public OutletMetricQueryRelation QueriesRelation { get; set; }

        public DataTypeEnum DataType { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public Frequency? Frequency { get; set; }

        public DayOfWeek? WeekStartDay { get; set; }

        public string ParameterReferences { get; set; }

        [Column("HCCBSQLQuery")]
        public string HC<PERSON><PERSON><PERSON>Query { get; set; }
    }
}
