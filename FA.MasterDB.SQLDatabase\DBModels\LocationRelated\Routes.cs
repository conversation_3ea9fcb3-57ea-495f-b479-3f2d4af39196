﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("Routes")]
    public class Routes
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string ErpId { get; set; }
        public bool IsDeactive { get; set; }
        public long CompanyId { get; set; }
    }
}
