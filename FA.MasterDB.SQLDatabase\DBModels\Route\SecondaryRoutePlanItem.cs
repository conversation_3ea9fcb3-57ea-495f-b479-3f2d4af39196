﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.Route;

public class SecondaryRoutePlanItem
{
    public long Id { get; set; }

    public int DayNumber { get; set; }

    public long RoutePlanId { get; set; }

    public long? RouteId { get; set; }

    public string ReasonCategory { get; set; }

    public string Reason { get; set; }

    public RouteClass Route { get; set; }

    public long? JWFieldUserId { get; set; }

    public RoutePlan RoutePlan { get; set; }
}
