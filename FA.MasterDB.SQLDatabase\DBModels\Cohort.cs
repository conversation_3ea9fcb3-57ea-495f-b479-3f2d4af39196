﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("Cohorts")]
    public class Cohort
    {

        [Column("Id")]
        public long Id { get; set; }

        [Column("ExtraInfoJson")]
        public string ExtraInfoJson { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("Deleted")]
        public bool Deleted { get; set; }
    }
}
