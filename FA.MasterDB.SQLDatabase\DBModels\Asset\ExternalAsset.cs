﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Asset
{
    [Table("FACompanyExternalAssets")]
    public class ExternalAsset
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Url { get; set; }

        public long? UserId { get; set; }

        public bool VisibleToHierarchy { get; set; }

        [Column("Company")]
        public long CompanyId { get; set; }

        public Company Company { get; set; }
        public bool isDeleted { get; set; }
    }
}
