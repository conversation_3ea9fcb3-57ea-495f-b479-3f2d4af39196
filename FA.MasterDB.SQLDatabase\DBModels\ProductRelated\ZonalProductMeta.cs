﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("FAZonalProductsMeta")]
    public class ZonalProductMeta
    {
        public long Id { get; set; }

        public double? Price { get; set; }

        public string Schemes { get; set; }

        public bool? IsActive { get; set; }

        public DateTime AddedOn { get; set; }

        public string LocalName { get; set; }

        public bool? IsTopSelling { get; set; }

        public bool? IsPromoted { get; set; }

        public bool? IsFocused { get; set; }

        public decimal? PriceRegular { get; set; }

        public decimal? PriceSuperCash { get; set; }

        public decimal? PricePlacement { get; set; }

        [Column("CompanyZone")]
        public long CompanyZoneId { get; set; }

        [Column("CompanyProduct")]
        public long CompanyProductId { get; set; }
    }
}
