﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Journey
{
    [Table("JourneyCalendars")]
    public class JourneyCalendar
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public int ForMonths { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public int Year { get; set; }

        public string SelectedZones { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool Deleted { get; set; }

        public bool IsDeactive { get; set; }

        public long CompanyId { get; set; }

        public virtual ICollection<JourneyCycle> JourneyCycles { get; set; }

        public static JourneyCalendar CreateCalender(DateTime dayStartTime, int monthStartDay)
        {
            var calenderStartYear = dayStartTime.Year;
            var calenderSatrtMonth = 1;

            if (monthStartDay > 1)
            {
                calenderStartYear--;
                calenderSatrtMonth = 12;
            }

            var startDate = new DateTime(calenderStartYear, calenderSatrtMonth, monthStartDay);
            var endDate = startDate.AddDays(-1).AddYears(1);
            var journeyCalendar = new JourneyCalendar
            {
                StartDate = startDate,
                EndDate = endDate,
                ForMonths = 12,
                Year = monthStartDay > 1 ? calenderStartYear + 1 : calenderStartYear,
                JourneyCycles = JourneyCycle.GetCycles(startDate)
            };
            return journeyCalendar;
        }
    }
}
