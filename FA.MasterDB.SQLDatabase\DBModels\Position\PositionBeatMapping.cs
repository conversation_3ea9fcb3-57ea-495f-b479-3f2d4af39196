﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Position
{
    [Table("PositionBeatMapping")]
    public class PositionBeatMapping
    {
        public long Id { get; set; }

        [ForeignKey("PositionCode")]
        public long PositionId { get; set; }

        [ForeignKey("LocationBeat")]
        public long BeatId { get; set; }

        [Column("TimeAdded", TypeName = "datetime2")]
        public DateTime CreatedAt { get; set; }

        public long CompanyId { get; set; }

        public string CreationContext { get; set; }

        public bool IsDeleted { get; set; }

        public virtual PositionCode PositionCode { get; set; }

        public virtual LocationBeat LocationBeat { get; set; }

        public long? ProductDivisionId { get; set; }
    }
}
