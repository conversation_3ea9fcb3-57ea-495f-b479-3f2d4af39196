﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.Schemes;

namespace FA.MasterDB.SQLDatabase.DBModels.Coupons;

public class CouponScheme
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public bool IsDeleted { get; set; }

    public string Name { get; set; }

    public string ERPId { get; set; }

    public string Description { get; set; }

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public DateTime UpdatedOn { get; set; }

    public long TotalCoupons { get; set; }

    public ICollection<CouponSchemeSlab> CouponSchemeSlab { get; set; }

    public ICollection<SchemeCouponDistribution> SchemeCouponDistribution { get; set; }

    public ICollection<CouponMaster> Master { get; set; }
}
