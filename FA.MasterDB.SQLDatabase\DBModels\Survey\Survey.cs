﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace FA.MasterDB.SQLDatabase.DBModels.Survey;

public class Survey
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("fields")]
    public List<QuestionGroup> Fields { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    [JsonProperty("surveyType")]
    public SurveyType? SurveyType { get; set; }

    // Database fields
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonIgnore]
    public long CompanyId { get; set; }

    [Column("isSkippable")]
    [JsonProperty("isSkippable")]
    public bool IsSkippable { get; set; }

    [JsonProperty("isOnetime")]
    public bool SingleTime { get; set; }

    public bool Disable { get; set; }

    public bool? Deleted { get; set; }

    [NotMapped]
    [JsonProperty("outlets")]
    public IEnumerable<string> Outlets { get; set; }

    [Obsolete]
    [JsonProperty("visitSurveyType")]
    public SurveyVisitType SurveyVisitType { get; set; }

    public DateTime? EditedOn { get; set; }

    public DateTime CreatedOn { get; set; }

    public string SurveyConstraints { get; set; }
}
