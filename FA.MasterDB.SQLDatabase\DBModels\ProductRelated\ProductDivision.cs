﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("FAProductDivision")]
    public class ProductDivision
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
        public int OrderPosition { get; set; }
        public string? StandardUnit { get; set; }
    }
}
