﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace FA.MasterDB.SQLDatabase.DBModels.Survey;

public class QuestionGroup
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("displayOrder")]
    public int DisplayOrder { get; set; }

    [JsonIgnore]
    public bool Deactivated { get; set; }

    [Json<PERSON>onverter(typeof(StringEnumConverter))]
    [JsonProperty("groupType")]
    public GroupType GroupType { get; set; }

    [JsonProperty("questions")]
    public List<Question> Questions { get; set; }

    [JsonProperty("showCondition")]
    public string ShowCondition { get; set; }

    [JsonIgnore]
    public long JsonForm_Id { get; set; }

    [JsonProperty(nameof(Deleted))]
    public bool? Deleted { get; set; }

    [ForeignKey("JsonForm_Id")]
    [JsonIgnore]
    public Survey Survey { get; set; }
}
