﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories.IDistributor;
using FA.MasterDB.SQLDatabase.DBConnection;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.DistributorRepo
{
    public class DistributorAddressRepository : IDistributorAddressRepository
    {
        private readonly MasterDbContext masterDbContext;

        public DistributorAddressRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        public async Task<string?> GetBillingAddress(long companyId, long distributorId)
        {
            ValidateParameters(companyId, distributorId);

            return await GetAddress(companyId, distributorId, AddressType.Billing);
        }

        public async Task<string?> GetShippingAddress(long companyId, long distributorId)
        {
            ValidateParameters(companyId, distributorId);

            return await GetAddress(companyId, distributorId, AddressType.Shipping);
        }

        private async Task<string?> GetAddress(long companyId, long distributorId, AddressType addressType)
        {
            return await masterDbContext.DistributorAddresses
                .Where(d => d.CompanyId == companyId && d.EntityId == distributorId && !d.IsDeactive && d.AddressType == addressType)
                .Select(d => d.Address)
                .FirstOrDefaultAsync();
        }

        private static void ValidateParameters(long companyId, long distributorId)
        {
            if (companyId <= 0 || distributorId <= 0)
            {
                throw new ArgumentException("Invalid parameters. CompanyId and distributorId must be greater than 0.");
            }
        }
    }
}
