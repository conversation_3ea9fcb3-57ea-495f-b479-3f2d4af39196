﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Beatometer
{
    public class BeatometerRuleDetails
    {
        public long Id { get; set; }

        public long BeatometerRuleId { get; set; }

        public int Sequence { get; set; }

        public string TagName { get; set; }

        public string TagDescription { get; set; }

        public string TagConstraintDetails { get; set; }

        public string TagColour { get; set; }

        public long CompanyId { get; set; }

        public bool IsDeleted { get; set; }

        public BeatometerRule BeatometerRule { get; set; }

        [Column("TagIcon")]
        public string TagIcon { get; set; }
    }
}
