﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace FA.MasterDB.SQLDatabase.DBModels.Device
{
    public class DeviceToken
    {
        public DeviceToken()
        {
            CreatedAt = DateTime.UtcNow;
            LastUpdatedAt = DateTime.UtcNow;
            CreationContext = "From NS app";
        }

        public long Id { get; set; }

        public long EmployeeId { get; set; }

        [StringLength(2000)]
        public string HashedOTP { get; set; }

        [StringLength(50)]
        public string ContactNumber { get; set; }

        public DateTime CreatedAt { get; set; }

        public bool IsInValid { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long CompanyId { get; set; }

        [StringLength(250)]
        public string CreationContext { get; set; }

        public static DeviceToken Create(string contactNumber, long empId, string hashedString, long compId)
        {
            return new DeviceToken
            {
                ContactNumber = contactNumber,
                HashedOTP = hashedString,
                EmployeeId = empId,
                CompanyId = compId
            };
        }
    }
}
