﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.Beatometer
{
    public class BeatometerRule
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public string OutletConstraints { get; set; }

        public string GeographyConstraints { get; set; }

        public string Frequency { get; set; }

        public long CompanyId { get; set; }

        public bool IsDeleted { get; set; }

        public ICollection<BeatometerRuleDetails> BeatometerRuleDetails { get; set; }
    }
}
