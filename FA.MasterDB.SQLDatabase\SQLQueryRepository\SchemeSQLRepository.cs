﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories;
using FA.MasterDB.SQLDatabase.DBConnection;

namespace FA.MasterDB.SQLDatabase.SQLQueryRepository
{

    public class SchemeSQLRepository : ISchemeSQLRepository
    {
        public SchemeSQLRepository(MasterDbDataReader sQLMasterDbContext)
        {
            masterDbSqlDataReader = sQLMasterDbContext;
        }

        public MasterDbDataReader masterDbSqlDataReader { get; }

        public async Task<List<SchemeMinModel>> GetSchemeMin(long companyId, List<long> schemeIds)
        {
            var schemeIdsString = string.Join(",", schemeIds);

            var query = $@"SELECT 
                        s.Id, 
                        CASE s.Category
                            WHEN '1' THEN 'Primary'
                            WHEN '2' THEN 'Secondary'
                            ELSE 'Uncategorized' 
                        END AS Category,
                        s.ErpId,
                        CASE s.ISMrpBilling
                            WHEN 0 THEN s1.Payout
                            WHEN 1 THEN s1.MRPPayout
                            ELSE 0 
                        END AS Payout,
                        s1.Id AS SlabId,
                        s.SchemeStep,
                        s.PayoutType AS PayoutType
                    FROM schemes s 
                    JOIN schemeslabs s1 ON s.id = s1.schemeId
                    WHERE companyId = {companyId} AND s.id IN ({schemeIdsString})";

            var data = (await masterDbSqlDataReader.GetModelFromQueryAsync<SchemeMinModel>(query)).ToList();
            return data;

        }
    }
}
