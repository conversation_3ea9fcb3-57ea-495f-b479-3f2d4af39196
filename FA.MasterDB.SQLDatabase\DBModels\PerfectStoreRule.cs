﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("PerfectStoreRules")]
    public class PerfectStoreRule
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("Name")]
        [StringLength(64)]
        public string Name { get; set; }

        [Column("DisplayName")]
        [StringLength(64)]
        public string DisplayName { get; set; }

        [Column("Description")]
        [StringLength(128)]
        public string Description { get; set; }

        [Column("ERPId")]
        [StringLength(16)]
        public string ERPId { get; set; }

        [Column("Visibility")]
        [StringLength(128)]
        public string Visibility { get; set; }

        [Column("StartDate")]
        public DateTime StartDate { get; set; }

        [Column("EffectiveEndDate")]
        public DateTime? EffectiveEndDate { get; set; }

        [Column("IsRepeatable")]
        public bool IsRepeatable { get; set; }

        [Column("RepeatFrequencyInDays")]
        public int RepeatFrequencyInDays { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [Column("Deleted")]
        public bool Deleted { get; set; }
    }
}
