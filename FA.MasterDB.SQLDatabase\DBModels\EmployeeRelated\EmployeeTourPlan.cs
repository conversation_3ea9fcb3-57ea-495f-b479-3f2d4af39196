﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class EmployeeTourPlan
    {
        public long Id { get; set; }

        public DateTime DeviceTime { get; set; }

        public DateTime ServerTime { get; set; }

        public long EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EffectiveStartDate { get; set; }

        public DateTime EndDate { get; set; }

        public DateTime? ReviewedOn { get; set; }

        public TourPlanApprovedStatus ReviewedStatus { get; set; }

        public virtual ClientEmployee Employee { get; set; }

        public bool IsRepeatable { get; set; }

        public virtual ICollection<EmployeeTourPlanItem> EmployeeTourPlanItems { get; set; }

        public int ForDays { get; set; }

        public long? PositionCodeId { get; set; }
        public long? UpdatedFromId { get; set; }
        public long? ReviewedById { get; set; }

        public PortalUserRole? ReviewedByRole { get; set; }
    }
}
