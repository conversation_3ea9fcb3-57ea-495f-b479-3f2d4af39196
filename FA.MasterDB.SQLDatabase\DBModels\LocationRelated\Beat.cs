﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("LocationBeats")]
    public class Beat
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public long? TerritoryId { get; set; }
        public string ErpId { get; set; }
        public bool IsDeactive { set; get; }
        public Territory Territory { get; set; }
    }
}
