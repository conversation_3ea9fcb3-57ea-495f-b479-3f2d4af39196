﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.Gamification;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    public class CompanyKPI
    {
        public long Id { get; set; }

        public long KpiId { get; set; }

        public string CreationContext { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public KpiFrequency Frequency { get; set; }

        public KpiUserType UserType { get; set; }

        public KpiObjective Objective { get; set; }

        public KpiCalculation Calculation { get; set; }

        public KpiMeasure Measure { get; set; }

        public bool IsQualifier { get; set; }

        public string Description { get; set; }

        public KpiType KPIType { get; set; }

        public string SQLQuery { get; set; }

        public string TargetSQLQuery { get; set; }

        public bool IsDeactivated { get; set; }

        public string Name { get; set; }

        public string UIName { get; set; }

        public int Sequence { get; set; }

        public long CompanyId { get; set; }

        public virtual KPI Kpi { get; set; }

        public double KPIConstraint { get; set; }

        public Relation Relation { get; set; }

        public string MasterSQLQuery { get; set; }

        public string TransactionSQLQuery { get; set; }

        public DayOfWeek? WeekOfDay { get; set; }
    }
}
