﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using FA.MasterDB.SQLDatabase.Interfaces;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class EmployeeRouteDiversion : IDeviceEntity
    {
        public DateTime DeviceTime { get; set; }

        public DateTime ServerTime { get; set; }

        public long Id { get; set; }

        public long CompanyId { get; set; }

        public long EmployeeId { get; set; }

        public long AssignedRouteId { get; set; }

        public long SelectedRouteId { get; set; }

        [StringLength(2048)]
        public string Reason { get; set; }

        public bool IsPostDayStart { get; set; }

        public bool HasChanged { get; set; }

        public static EmployeeRouteDiversion Create(EmployeeRouteDiversion employeeRouteDiversion)
        {
            return new EmployeeRouteDiversion
            {
                EmployeeId = employeeRouteDiversion.EmployeeId,
                SelectedRouteId = employeeRouteDiversion.SelectedRouteId,
                AssignedRouteId = employeeRouteDiversion.AssignedRouteId,
                ServerTime = employeeRouteDiversion.ServerTime,
                DeviceTime = employeeRouteDiversion.DeviceTime,
                CompanyId = employeeRouteDiversion.CompanyId,
                HasChanged = employeeRouteDiversion.HasChanged,
                IsPostDayStart = employeeRouteDiversion.IsPostDayStart,
                Reason = employeeRouteDiversion.Reason,
            };
        }
    }
}
