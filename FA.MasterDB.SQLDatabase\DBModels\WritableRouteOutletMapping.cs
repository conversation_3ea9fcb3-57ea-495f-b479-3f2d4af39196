﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("RouteOutletMappings")]
    public class WritableRouteOutletMapping
    {
        public long CompanyId { get; set; }

        public long Id { get; set; }

        public long RouteId { get; set; }

        public long LocationId { get; set; }

        public bool Deleted { get; set; }
    }
}
