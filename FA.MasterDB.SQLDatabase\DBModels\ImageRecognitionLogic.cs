﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class ImageRecognitionLogic
    {
        public long Id { get; set; }

        public string RuleName { get; set; }

        public long CompanyId { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime StartDate { get; set; }

        public bool IsMandatory { get; set; }

        public DateTime EndDate { get; set; }

        public string OutletHierarchyConstraints { get; set; }

        public string OutletConstraints { get; set; }

        public string ProductConstraints { get; set; }

        public string Assets { get; set; }

        public string KPIWeightage { get; set; }

        public IRPositioning? Positioning { get; set; }
    }
}
