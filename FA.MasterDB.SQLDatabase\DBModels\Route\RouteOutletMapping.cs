﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Route;

public class RouteOutletMapping
{
    public long Id { get; set; }

    public long RouteId { get; set; }

    public long LocationId { get; set; }

    public bool Deleted { get; set; }

    public long CompanyId { get; set; }

    public virtual RouteClass Route { get; set; }

    public int? VisitOrder { get; set; }

    public virtual LocationDB Location { get; set; }
}
