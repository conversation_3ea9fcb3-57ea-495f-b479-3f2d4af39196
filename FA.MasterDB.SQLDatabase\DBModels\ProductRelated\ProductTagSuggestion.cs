﻿// Copyright (c) FieldAssist. All Rights Reserved.
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("ProductTagSuggestions")]
    public class ProductTagSuggestion
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("OutletId")]
        public long OutletId { get; set; }

        [Column("ProductId")]
        public long ProductId { get; set; }

        [Column("SuggestedQuantity")]
        public long SuggestedQuantity { get; set; }

        [Column("ProductTagId")]
        public long ProductTagId { get; set; }

        [Column("MTDQty")]
        public long MTDQty { get; set; }

        [Column("IsCompleted")]
        public bool IsCompleted { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("CreationContext")]
        [StringLength(64)]
        public string CreationContext { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [ForeignKey("ProductTagId")]
        public ProductTagMaster ProductTagMasters { get; set; }
    }
}
