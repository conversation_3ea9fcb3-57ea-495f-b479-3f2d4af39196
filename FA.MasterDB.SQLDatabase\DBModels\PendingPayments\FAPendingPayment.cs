﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.PendingPayments
{
    public class FAPendingPayment
    {
        public long Id { get; set; }

        public decimal? OutstandingAmount { get; set; }

        public decimal? OverdueAmount { get; set; }

        public decimal? DaylockAmount { get; set; }

        public int? Ageing { get; set; }

        public decimal? CreditLimit { get; set; }

        public decimal? ShippableCreditLimit { get; set; }

        public decimal? CashDiscountAvailable { get; set; }

        public decimal? LastYearTillDateSales { get; set; }

        public decimal? YearTillDateSales { get; set; }

        public decimal? LastYearTotalSales { get; set; }

        public DateTime? ServerTime { get; set; }

        public decimal? TotalOrderValue { get; set; }

        public decimal? AvgSalesValue { get; set; }

        public decimal? AvgSalesQty { get; set; }

        public decimal? ProductLinesCutsPerCall { get; set; }

        public decimal? SkuLinesCutsPerCall { get; set; }

        public DateTime? LastProdVisitsDate { get; set; }

        public DateTime? LastVisitDate { get; set; }

        public long LocationId { get; set; }

        public long CompanyId { get; set; }

        public bool IsActive { get; set; }

        public List<FAPendingPaymentInvoice> Invoices { get; set; }
    }
}
