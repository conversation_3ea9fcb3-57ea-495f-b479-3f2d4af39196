﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace Libraries.CommonEnums;

public enum Relation
{
    [Display(Name = "Use Direct Report Db")]
    UseDirectReportDb,

    [Display(Name = "Use Direct Master Db")]
    UseDirectMasterDb,

    [Display(Name = "Report Db / Master Db")]
    ReportDbDividedByMasterDb,

    [Display(Name = "Master Db / Report Db")]
    MasterDbDividedByReportDb,

    [Display(Name = "Use Direct Transaction Db")]
    UseDirectTransactionDb,

    [Display(Name = "Transaction Db / Master Db")]
    TransactionDbDividedByMasterDb,

    [Display(Name = "Master Db / Transaction Db")]
    MasterDbDividedByTransactionDb,

    [Display(Name = "Master Db / DMS Db")]
    MasterDbDividedByDmsDb = 10,

    [Display(Name = "DMS Db / Report Db")]
    DmsDbDividedByReportDb = 11,

    [Display(Name = "DMS Db / Master Db")]
    DmsDbDividedByMasterDb = 12,

    [Display(Name = "Report Db / DMS Db")]
    ReportDbDividedByDmsDb = 13,
}
