﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Coupons
{
    public class CouponMaster
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public string CouponCodeId { get; set; }

        public long GiftId { get; set; }

        public string GiftName { get; set; }

        public long CouponSchemeId { get; set; }

        public CouponScheme CouponScheme { get; set; }

        public long? CouponSchemeSlabId { get; set; }

        public long? OrderId { get; set; }

        public long? Outletid { get; set; }

        public CouponStatus CouponStatus { get; set; }

        public DateTime? AssignedOn { get; set; }

        public long? AssinedToUser { get; set; }

        public int? AssinedToUserRole { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime ExpiryDate { get; set; }

        public double? OrderValue { get; set; }
    }
}
