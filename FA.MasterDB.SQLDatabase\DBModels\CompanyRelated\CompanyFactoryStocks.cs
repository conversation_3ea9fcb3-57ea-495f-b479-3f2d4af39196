﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    [Table("CompanyFactoryStocks")]
    public class CompanyFactoryStocks
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("FactoryId")]
        public long FactoryId { get; set; }

        [Column("ProductId")]
        public long ProductId { get; set; }

        [Column("Quantity")]
        public double Quantity { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }
    }
}
