﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.Route;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

public class EmployeeRouteMapping
{
    public long Id { get; set; }

    public long EmployeeId { get; set; }

    public long RouteId { get; set; }

    [Column("CreatedAt")]
    public DateTime TimeAdded { get; set; }

    public long CompanyId { get; set; }

    public string CreationContext { get; set; }

    public bool IsDeleted { get; set; }

    public ClientEmployee Employee { get; set; }

    public RouteClass Route { get; set; }
}
