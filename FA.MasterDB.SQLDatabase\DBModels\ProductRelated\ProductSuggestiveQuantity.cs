﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("ProductSuggestiveQuantities")]
    public class ProductSuggestiveQuantity
    {
        public long Id { get; set; }

        public decimal? SuggestiveQuantity { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long LocationId { get; set; }

        public long CompanyProductId { get; set; }

        public long CompanyId { get; set; }

        public LocationDB Location { get; set; }

        // public CompanyProduct CompanyProduct { set; get; }
        public Company Company { get; set; }
    }
}
