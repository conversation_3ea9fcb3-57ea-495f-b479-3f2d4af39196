﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Journey
{
    [Table("JourneyCycles")]
    public class JourneyCycle
    {
        public long Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public int MonthNumber { get; set; }

        public string MonthName { get; set; }

        public DateTime MonthStartDate { get; set; }

        public DateTime MonthEndDate { get; set; }

        public long JourneyCalendarId { get; set; }

        public long CompanyId { get; set; }

        public virtual JourneyCalendar JourneyCalendar { get; set; }

        public ICollection<JourneyWeek> JourneyWeeks { get; set; }

        public static ICollection<JourneyCycle> GetCycles(DateTime startDate)
        {
            var cycles = new List<JourneyCycle>();
            for (var j = 0; j < 12; j++)
            {
                var monthStartDate = startDate.AddMonths(j);
                var monthEndDate = monthStartDate.AddMonths(1).AddDays(-1);
                cycles.Add(new JourneyCycle
                {
                    MonthStartDate = monthStartDate,
                    MonthEndDate = monthEndDate,
                    MonthNumber = j + 1
                });
            }

            return cycles;
        }
        public class JourneyCycleMonthYear
        {
            public int Month { get; set; }

            public int Year { get; set; }

            public DateTime MonthStartDate { get; set; }

            public DateTime MonthEndDate { get; set; }

            public string MonthName { get; set; }

            public ICollection<JourneyWeekDataModel> Weeks { get; set; }
        }

        public class JourneyWeekDataModel
        {
            public int WeekForMonth { get; set; }

            public int QuarterNumber { get; set; }

            public int WeekForQuarter { get; set; }

            public int WeekForYear { get; set; }

            public DateTime WeekStartDate { get; set; }

            public DateTime WeekEndDate { get; set; }
        }
    }
}
