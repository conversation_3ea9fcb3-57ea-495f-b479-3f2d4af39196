﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.TertiaryRelated
{
    [Table("TertiaryEntities")]
    public class TertiaryEntity
    {
        public long Id { get; set; }

        public string Name { get; set; }

        public string ErpId { get; set; }

        public long CompanyId { get; set; }

        public string OwnersName { get; set; }

        public string OwnersNo { get; set; }

        public string EmailId { get; set; }

        public string Address { get; set; }

        public string City { set; get; }

        public string State { get; set; }

        public string PinCode { get; set; }

        public string ImageId { set; get; }

        public double? AttributeNumber1 { get; set; }

        public double? AttributeNumber2 { get; set; }

        public double? AttributeNumber3 { get; set; }

        public string AttributeText1 { get; set; }

        public string AttributeText2 { get; set; }

        public string AttributeText3 { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool IsDeleted { get; set; }

        public bool IsDeactive { get; set; }
    }
}
