﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;

namespace Libraries.CommonEnums;

public enum AlertAction
{
    Pending,
    Approved,
    Disapproved,
    Archived
}

public enum ClientPlatform
{
    Dashboard = 10,
    ManagerApp = 20,
    ManagerAppGT = 30,
    ManagerAppMT = 40
}

public enum SaleType
{
    All,
    PreSales,
    VanSales,
    DirectSales
}

public enum DbUpdateSource
{
    Unknown = 0,
    MissingDaystartProcessor = 1,
    MissingDaystartProcessor_NonDayEnd = 2,
    ReportProcessor = 3,
    ReportProcessor_SlowQueue = 4,
    ReportProcessor_UnsyncedQueue = 5,
    ReportProcessor_NonWorkingQueue = 6,
    ReportProcessor_TestCase = 7,
    ReportProcessor_NonWorkingUnsyncedQueue = 8,
    ReportProcessor_InvalidateSales = 9,
    ReportProcessor_VanSales = 10
}

public enum GeographicalHierarchyLevel
{
    Zone = 0,
    Region = 1
}

public enum PortalUserRole
{
    GlobalAdmin = 0,
    AccountManager = 3,
    FinanceHead = 4,
    ChannelPartner = 6,
    CompanyAdmin = 10,
    CompanyExecutive = 12,
    FactoryAdmin = 15,
    GlobalSalesManager = 20,
    NationalSalesManager = 30,
    ZonalSalesManager = 40,
    RegionalAdmin = 45,
    RegionalSalesManager = 50,
    AreaSalesManager = 60,
    Distributor = 70,
    SuperStockist = 71,
    SubDistributor = 72,
    ClientEmployee = 80,
    Retailer = 90,
    ConglomerateUser = 200,
    Unknown = 1000,
}

public enum UserLoginStatus
{
    NA = 0,
    Login = 1,
    NoLogin = 2
}

public enum QuickVizPlan
{
    Basic = 1,
    Premium = 2,
    Advance = 3
}
public enum OutletStatus
{
    All,
    Active,
    Blocked,
}
public enum SubscribedScreenType
{
    ManagerApp,
    Dashboard,
}

//Use enum PositionCodeLevel
[Obsolete]
public enum PositionLevelNomenclature
{
    L8Position = 8,
    L7Position = 9,
    L6Position = 10,
    L5Position = 20,
    L4Position = 30,
    L3Position = 40,
    L2Position = 50,
    L1Position = 60
}
