﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Position
{
    public class PositionDistributorMapping
    {
        public long Id { get; set; }

        [ForeignKey("PositionCode")]
        public long PositionId { get; set; }

        [ForeignKey("Distributor")]
        public long DistributorId { get; set; }

        [Column("TimeAdded", TypeName = "datetime2")]
        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        [ForeignKey("Company")]
        public long CompanyId { get; set; }

        public string CreationContext { get; set; }

        public bool IsDeleted { get; set; }

        public virtual PositionCode PositionCode { get; set; }

        public virtual Distributor Distributor { get; set; }
    }
}
