﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    public class ProductPricingMasters
    {
        public long Id { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long CompanyId { get; set; }

        public long ProductId { get; set; }

        public long? BatchId { get; set; }

        public long? RegionId { get; set; }

        public long? DistributorId { get; set; }

        public long? SubStockistId { get; set; }

        public long? SuperStockistId { get; set; }

        public decimal MRP { get; set; }

        public decimal PTR { get; set; }

        public decimal? PTSubStockist { get; set; }

        public decimal PTD { get; set; }

        public decimal? PTSuperStockist { get; set; }

        public PricingMastertype PricingMasterType { get; set; }

        public long? DistributorSegmentationId { get; set; }

        public long? DistributorChannelId { get; set; }

        public decimal? PTR_MT { get; set; }

        [Column("BatchNumber")]
        public string BatchNumber { get; set; }
    }
}
