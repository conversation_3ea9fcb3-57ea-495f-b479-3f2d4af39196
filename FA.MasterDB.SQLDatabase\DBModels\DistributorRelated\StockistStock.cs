﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class StockistStock
    {
        public long Id { get; set; }

        public long DistributorId { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public long CompanyId { get; set; }

        public virtual ICollection<StockistStockItem> Items { get; set; }
    }
}
