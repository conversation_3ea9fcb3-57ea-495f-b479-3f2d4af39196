﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    [Table("EmployeeProductDivisionMappings")]
    public class EmployeeProductDivisionMappings
    {
        public long Id { get; set; }
        public long EmployeeId { get; set; }
        public long ProductDivisionId { get; set; }
        public DateTime CreatedAt { get; set; }
        public long CompanyId { get; set; }
        public string? CreationContext { set; get; }
        public bool Deleted { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
