﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.Asset;

namespace FA.MasterDB.SQLDatabase.DBModels;

public class EquipmentMaster
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public string CreationContext { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime LastUpdatedAt { get; set; }

    public bool IsActive { get; set; }

    public string Name { get; set; }

    public string ErpId { get; set; }

    public string ReferenceNumber { get; set; }

    public DateTime? MFGDate { get; set; }

    public int WarrantyPeriod { get; set; }

    [ForeignKey("AssetDefinition")]
    public long AssetDefinitionId { get; set; }

    public virtual AssetDefinition AssetDefinition { get; set; }

    public string AttributeText1 { get; set; }

    public string AttributeText2 { get; set; }

    public string AttributeText3 { get; set; }

    public string AttributeText4 { get; set; }

    public long? AttributeNumber1 { get; set; }

    public long? AttributeNumber2 { get; set; }

    public long? AttributeNumber3 { get; set; }

    public long? AttributeNumber4 { get; set; }

    public DateTime? AttributeDate1 { get; set; }

    public DateTime? AttributeDate2 { get; set; }

    public bool? AttributeBool1 { get; set; }

    public bool? AttributeBool2 { get; set; }
}
