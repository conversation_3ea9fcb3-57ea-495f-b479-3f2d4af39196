﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using System.Data;
using System.Linq;
using FileGenerator.HelperModels;

namespace FileGenerator.DataTableHelpers
{
    public class SeparateTablesForPivotTable
    {
        private static void RemoveColumn(string dcName, DataTable datatable)
        {
            var dcCollection = datatable.Columns;
            if (dcCollection.Contains(dcName))
            {
                dcCollection.Remove(dcName);
            }
        }

        public static DataSet SeparateTables(DataTable dt, PivotColumn pivotColumn, string baseTableName)
        {
            var columns = new List<string> { "Id", pivotColumn.ParentColumn };
            if (pivotColumn.SecondaryPivotColumn != null)
            {
                columns.Add(pivotColumn.SecondaryPivotColumn);
            }

            columns.AddRange(pivotColumn.ValueColumns);
            var forPivot = dt.AsEnumerable().CopyToDataTable();
            var columnsToDelete = forPivot.Columns.Cast<DataColumn>().Select(d => d.ColumnName).ToList();
            foreach (var column in columnsToDelete)
            {
                if (!columns.Contains(column))
                {
                    RemoveColumn(column, forPivot);
                }
            }

            var pivotTable =
                forPivot.AsEnumerable().Distinct(DataRowComparer.Default)
                    .CopyToDataTable(); //dt.DefaultView.ToTable(true, columns.ToArray());

            //flat table separation
            foreach (var column in columns)
            {
                if (column != "Id")
                {
                    RemoveColumn(column, dt);
                }
            }
            //dt = dt.DefaultView.ToTable(true);
            dt = dt.AsEnumerable().Distinct(DataRowComparer.Default).CopyToDataTable();
            dt.TableName = baseTableName;

            //pivot table creation
            pivotTable.TableName = PivotColumn.PivotTableName;
            var dataset = new DataSet();
            dataset.Tables.Add(dt);
            dataset.Tables.Add(pivotTable);
            return dataset;
        }

        public static DataSet SeparateTables(DataTable data, DataTable horizontalSubgroup, PivotColumn pivotColumn,
            string baseTableName)
        {
            var columns = new List<string> { "Id", pivotColumn.ParentColumn };
            if (pivotColumn.SecondaryPivotColumn != null)
            {
                columns.Add(pivotColumn.SecondaryPivotColumn);
            }

            columns.AddRange(pivotColumn.ValueColumns);
            var forPivot = data.AsEnumerable().CopyToDataTable();
            var forhorizontalSubgroup = horizontalSubgroup.AsEnumerable().CopyToDataTable();
            var columnsToDelete = forPivot.Columns.Cast<DataColumn>().Select(d => d.ColumnName).ToList();
            foreach (var column in columnsToDelete)
            {
                if (!columns.Contains(column))
                {
                    RemoveColumn(column, forPivot);
                    RemoveColumn(column, forhorizontalSubgroup);
                }
            }

            var pivotTable =
                forPivot.AsEnumerable().Distinct(DataRowComparer.Default)
                    .CopyToDataTable(); //dt.DefaultView.ToTable(true, columns.ToArray());
            pivotTable = forPivot.AsEnumerable().Distinct(DataRowComparer.Default)
                .Concat(forhorizontalSubgroup.AsEnumerable().Distinct(DataRowComparer.Default)).CopyToDataTable();

            //flat table separation
            foreach (var column in columns)
            {
                if (column != "Id")
                {
                    RemoveColumn(column, data);
                }
            }
            //dt = dt.DefaultView.ToTable(true);
            data = data.AsEnumerable().Distinct(DataRowComparer.Default).CopyToDataTable();
            data.TableName = baseTableName;

            //pivot table creation
            pivotTable.TableName = PivotColumn.PivotTableName;
            var dataset = new DataSet();
            dataset.Tables.Add(data);
            dataset.Tables.Add(pivotTable);
            return dataset;
        }
    }
}
