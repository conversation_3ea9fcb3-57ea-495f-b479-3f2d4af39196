﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("OutletOnboardingDetails")]
    public class OutletOnboardingDetails
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("AssigneeRole")]
        public int AssigneeRole { get; set; }

        [Column("AssigneeId")]
        public long AssigneeId { get; set; }

        [Column("AssignedToUserId")]
        public long? AssignedToUserId { get; set; }

        [Column("Status")]
        public Status Status { get; set; }

        [Column("ShopName")]
        public string ShopName { get; set; }

        [Column("Latitude")]
        public decimal? Latitude { get; set; }

        [Column("Longitude")]
        public decimal? Longitude { get; set; }

        [Column("Market")]
        public long? Market { get; set; }

        [Column("Address")]
        public string Address { get; set; }

        [Column("OwnersName")]
        public string OwnersName { get; set; }

        [Column("OwnersNo")]
        public string OwnersNo { get; set; }

        [Column("TIN")]
        public string TIN { get; set; }

        [Column("Email")]
        public string Email { get; set; }

        [Column("PinCode")]
        public string PinCode { get; set; }

        [Column("MarketName")]
        public string MarketName { get; set; }

        [Column("City")]
        public string City { get; set; }

        [Column("State")]
        public string State { get; set; }

        [Column("ImageId")]
        public string ImageId { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("GSTIN")]
        public string GSTIN { get; set; }

        [Column("PAN")]
        public string PAN { get; set; }

        [Column("Aadhar")]
        public string Aadhar { get; set; }

        [Column("AttributeText1")]
        public string AttributeText1 { get; set; }

        [Column("AttributeText2")]
        public string AttributeText2 { get; set; }

        [Column("AttributeText3")]
        public string AttributeText3 { get; set; }

        [Column("GSTRegistered")]
        public bool GSTRegistered { get; set; }

        [Column("BankAccountNumber")]
        public string BankAccountNumber { get; set; }

        [Column("AccountHoldersName")]
        public string AccountHoldersName { get; set; }

        [Column("IFSCCode")]
        public string IFSCCode { get; set; }

        [Column("LandMark")]
        public string LandMark { get; set; }

        [Column("LandlineNumber")]
        public string LandlineNumber { get; set; }

        [Column("AlternateImageId")]
        public string AlternateImageId { get; set; }

        [Column("PhotoProofId")]
        public string PhotoProofId { get; set; }

        [Column("TypeOfIdProof")]
        public string TypeOfIdProof { get; set; }

        [Column("ModeOfDataCollection")]
        public string ModeOfDataCollection { get; set; }

        [Column("AttributeText4")]
        public string AttributeText4 { get; set; }

        [Column("AttributeNumber1")]
        public float? AttributeNumber1 { get; set; }

        [Column("AttributeNumber2")]
        public float? AttributeNumber2 { get; set; }

        [Column("AttributeNumber3")]
        public float? AttributeNumber3 { get; set; }

        [Column("AttributeNumber4")]
        public float? AttributeNumber4 { get; set; }

        [Column("AttributeBoolean1")]
        public bool? AttributeBoolean1 { get; set; }

        [Column("AttributeBoolean2")]
        public bool? AttributeBoolean2 { get; set; }

        [Column("AttributeDate1")]
        public DateTime? AttributeDate1 { get; set; }

        [Column("AttributeDate2")]
        public DateTime? AttributeDate2 { get; set; }

        [Column("AttributeImage1")]
        public string AttributeImage1 { get; set; }

        [Column("AttributeImage2")]
        public string AttributeImage2 { get; set; }

        [Column("AttributeImage3")]
        public string AttributeImage3 { get; set; }

        [Column("District")]
        public string District { get; set; }

        [Column("FSSAINumber")]
        public string FSSAINumber { get; set; }

        [Column("FSSAIExpiryDate")]
        public DateTime? FSSAIExpiryDate { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("AssigneePositionId")]
        public long? AssigneePositionId { get; set; }

        [Column("AssignedToPositionId")]
        public long? AssignedToPositionId { get; set; }

        [Column("SuggestedUsersPositionId")]
        public string SuggestedUsersPositionId { get; set; }

        [Column("SuggestedUsersId")]
        public string SuggestedUsersId { get; set; }
    }
}
