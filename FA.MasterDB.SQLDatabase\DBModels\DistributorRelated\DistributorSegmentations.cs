﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    [Table("DistributorSegmentations")]
    public class DistributorSegmentations
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("IsDeactive")]
        public bool IsDeactive { get; set; }

        [Column("Deleted")]
        public bool Deleted { get; set; }

        [Column("Name")]
        public string Name { get; set; }

        [Column("ERPId")]
        public string ErpId { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }
    }
}
