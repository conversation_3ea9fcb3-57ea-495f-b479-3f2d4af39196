﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using Libraries.CommonEnums;

namespace FA.MasterDB.Core.Repositories.ILocation
{
    public interface IOutletSegmentationAttributesRepository
    {
        Task<List<OutletSegmentationAttributeDTO>> GetAllSegments(long companyId);
        Task<OutletSegmentationAttributeDTO> GetOutletSegment(long companyId,
            OutletSegmentation outletSegmentation);
    }
}
