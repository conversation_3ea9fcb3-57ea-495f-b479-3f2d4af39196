﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes;

public class QualifierSchemeSlab
{
    public long Id { get; set; }

    public long? ProductId { get; set; }

    public long? Quantity { get; set; }

    public long? BasketId { get; set; }

    public bool IsBasketMandatory { get; set; }

    public long SchemeSlabId { get; set; }

    public virtual SchemeSlab SchemeSlab { get; set; }
}
