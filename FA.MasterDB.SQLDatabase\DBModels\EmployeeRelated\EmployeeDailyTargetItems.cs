﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

[Table("EmployeeDailyTargetItems")]
public class EmployeeDailyTargetItems
{
    public long Id { get; set; }

    public long EmployeeDailyTargetsId { get; set; }

    public long? TargetValue { get; set; }

    public DateTime ItemDate { get; set; }

    public EmployeeDailyTargets EmployeeDailyTargets { get; set; }
}
