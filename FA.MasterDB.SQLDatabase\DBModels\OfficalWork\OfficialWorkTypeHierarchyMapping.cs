﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.OfficalWork
{
    public class OfficialWorkTypeHierarchyMapping
    {
        public long Id { get; set; }

        public PortalUserRole HierarchyLevel { get; set; }

        public long OfficialWorkTypeId { get; set; }

        [Column("ComapnyId")]
        public long CompanyId { get; set; }

        public OfficialWorkTypes OfficialWorkType { get; set; }

        public int? PositionCodeLevel { get; set; }
    }
}
