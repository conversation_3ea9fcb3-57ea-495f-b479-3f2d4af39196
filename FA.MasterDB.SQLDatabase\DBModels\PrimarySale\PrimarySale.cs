﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.PrimarySale
{
    public class PrimarySale
    {
        public long Id { get; set; }

        public DateTime CreatedAt { get; set; }

        public string InvoiceNumber { get; set; }

        public DateTime InvoiceDate { get; set; }

        public long DistributorId { get; set; }

        public long CompanyId { get; set; }

        public bool IsReceived { get; set; }

        public DateTime? ReceivedOn { get; set; }

        public long Month { get; set; }

        public long Year { get; set; }

        public long Day { get; set; }

        public List<PrimarySaleItems> PrimarySaleItems { get; set; }
    }
}
