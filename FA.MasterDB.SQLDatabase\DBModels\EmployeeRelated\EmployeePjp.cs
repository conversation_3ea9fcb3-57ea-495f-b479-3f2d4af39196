﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class EmployeePjp
    {
        public long Id { get; set; }

        public long? EmployeeId { get; set; }

        public long CompanyId { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EffectiveDate { get; set; }

        public DateTime? DeactivationDate { get; set; }

        public bool Deleted { get; set; }

        public bool IsDeactive { get; set; }

        public JourneyFrequency PJPFrequency { get; set; }

        public ICollection<PjpOutletMapping> PjpOutletMappings { get; set; }
    }
}
