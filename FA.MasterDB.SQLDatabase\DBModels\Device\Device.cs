﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Device;

public class Device
{
    [Key]
    public long DeviceID { get; set; }

    public Guid DeviceIdentifier { get; set; }

    [ForeignKey("Employee")]
    public long EmployeeID { get; set; }

    // public String EmployeeName { get; set; }
    public long CompanyID { get; set; }

    public string OSVersion { get; set; }

    public int SdkVersion { get; set; }

    public string Manufacturer { get; set; }

    public string Brand { get; set; }

    public string Model { get; set; }

    public string Serial { get; set; }

    public int AppVersionNumber { get; set; }

    public string AppVersionName { get; set; }

    public string GcmId { get; set; }

    public DateTime AddedOn { get; set; }

    public DateTime LastUpdated { get; set; }

    public DateTime LastSeenAt { get; set; }

    public DeviceStatus Status { get; set; }

    public bool DataReloadRequired { get; set; }

    public virtual ClientEmployee Employee { get; set; }

    [Column("MigrateToFlutter")]
    public bool MigrateToFlutter { get; set; } = false;

    [StringLength(64)]
    public string IMEINo { get; set; }

    [Column("AlertSource")]
    public AlertSource? AlertSource { get; set; }
}
