﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.IDistributor;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.Mapping;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.DistributorRepo
{

    public class BilledToShippedToAddressRepository : IBilledToShippedToAddressRepository
    {
        private readonly MasterDbContext masterDbContext;
        public BilledToShippedToAddressRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }
        public async Task<List<BilledtoshippedtoAddressInfo>> GetAddressInfoByIds(List<long> ids)
        {
            return await masterDbContext.BilledtoshippedtoAddress.Where(s => ids.Contains(s.Id) && s.IsActive)
                .Select(s =>
                BilledToShippedToAddressMapping.
                CreateObject_BilledtoshippedtoAddressInfo(s))
                .ToListAsync();
        }
    }
}
