﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("FAProductPrimaryCategory")]
    public class ProductPrimaryCategory
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public string Name { get; set; }
        public string? StandardUnit { get; set; }
        public long ProductDivisionId { get; set; }
        public ProductDivision ProductDivision { get; set; }
        public int OrderPosition { get; set; }
    }
}
