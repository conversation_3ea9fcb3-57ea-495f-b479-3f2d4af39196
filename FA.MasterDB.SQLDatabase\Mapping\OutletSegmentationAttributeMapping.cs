﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class OutletSegmentationAttributeMapping
    {
        public static OutletSegmentationAttributeDTO CreateObject_OutletSegmentationAttribute
            (OutletSegmentationAttribute outletSegmentationAttribute)
        {
            return new OutletSegmentationAttributeDTO()
            {
                ErpId = outletSegmentationAttribute.ErpId,
                Id = outletSegmentationAttribute.Id,
                Segmentation = outletSegmentationAttribute.Segmentation
            };
        }
    }
}
