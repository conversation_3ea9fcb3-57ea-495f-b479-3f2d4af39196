﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.Core.BaseModels
{
    public class ChannelEntity
    {
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public OutletChannel Enum { get; set; }
        public string DefaultName { get; set; }
        public string CustomName { get; set; }
        public string ErpId { get; set; }
        public bool IsValid { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
    }
}
