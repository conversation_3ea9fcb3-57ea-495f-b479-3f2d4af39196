﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    [Table("CompanyDefinedProductTypes")]
    public class CompanyDefinedProductType
    {
        public long Id { get; set; }

        public ProductType ProductType { get; set; }

        public decimal? SuggestiveQuantity { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long LocationId { get; set; }

        public long CompanyProductId { get; set; }

        public long CompanyId { get; set; }

        public LocationDB Location { get; set; }

        public ProductTable CompanyProduct { get; set; }

        public Company Company { get; set; }
    }
}
