﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class Reward
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public bool Deleted { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string ExtraInfoJson { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public int Type { get; set; }

        public long? EntityId { get; set; }
    }
}
