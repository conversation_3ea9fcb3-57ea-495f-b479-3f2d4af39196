﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes
{
    public class InternationalDiscount
    {
        public long Id { get; set; }

        public long CompanyId { get; }

        public string DiscountName { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime StartDate { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime EndDate { get; set; }

        public string Segmentation { get; set; }

        public long? FirstLevelDiscount { get; set; }

        public long? SecondLevelDiscount { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("IsDeleted")]
        public bool IsDeleted { get; set; }

        public bool FirstlevelDiscount_IsEditable { get; set; }

        public bool SecondlevelDiscount_IsEditable { get; set; }

        public bool FirstLevelDiscount_AutoApplied { get; set; }

        public bool SecondLevelDiscount_AutoApplied { get; set; }

        public bool FOC_Applicable { get; set; }

        public bool FOC_Mandatory { get; set; }

        public bool? IsDiscountAppliedatProductLevel { get; set; }

        public long? FOCPayoutPercentage { get; set; }
    }
}
