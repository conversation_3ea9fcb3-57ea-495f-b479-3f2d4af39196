﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.Core.DTOs
{
    public class LocationWithGeoHierarchy
    {
        public long Id { get; set; }
        public string ShopName { get; set; }
        public string OwnersName { get; set; }
        public string OwnersNo { get; set; }
        public string State { get; set; }
        public string City { get; set; }
        public string Address { get; set; }
        public string PinCode { get; set; }
        public string GSTIN { get; set; }
        public string TIN { get; set; }
        public string PAN { get; set; }
        public string Territory { get; set; }
        public string Region { get; set; }
        public string Zone { get; set; }
        public string Beat { get; set; }
        public string ErpId { get; set; }
        public string BeatErpId { get; set; }
        public long? BeatId { get; set; }
        public long? TerritoryId { get; set; }
        public long? RegionId { get; set; }
        public long? ZoneId { get; set; }
        public string EmailId { get; set; }
        public string ShopType { get; set; }
        public long ChannelId { get; set; }
        public string ChannelType { get; set; }
        public long? ShopTypeId { get; set; }
        public long OutletSegmentationId { get; set; }
        public OutletSegmentation Segmentation { get; set; }
        public OutletChannel OutletChannel { get; set; }
        public string FSSAINumber { get; set; }
    }
}
