﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Newtonsoft.Json;
using StackExchange.Redis;

namespace Libraries.CacheProvider
{
    public class RedisCacheProvider
    {
        private readonly IDatabase cache;

        public RedisCacheProvider(string redisConnectionString)
        {
            var redis = ConnectionMultiplexer.Connect(ConfigurationOptions.Parse(redisConnectionString));
            cache = redis.GetDatabase();
        }

        /// <summary>
        /// supports Expiration
        /// </summary>
        /// <param name="cacheKey"></param>
        /// <param name="result"></param>
        /// <param name="expiresIn">Time after whihc Key will be removed</param>
        public void Insert<T>(string cacheKey, T result, TimeSpan expiresIn)
        {
            var modelS = JsonConvert.SerializeObject(result);
            cache.StringSet(cacheKey, modelS, expiresIn);
        }

        public bool TryGet<T>(string cacheKey, out T result)
        {
            result = default(T);
            try
            {
                var m = cache.StringGet(cacheKey);
                if (m.IsNull)
                {
                    result = JsonConvert.DeserializeObject<T>(m);
                    return true;
                }
            }
            catch { }

            return false;
        }
    }
}
