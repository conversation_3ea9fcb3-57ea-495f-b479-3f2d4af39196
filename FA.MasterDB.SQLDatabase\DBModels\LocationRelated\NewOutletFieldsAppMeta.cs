﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class NewOutletFieldsAppMeta
    {
        public long Id { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public NewOutletFieldsFor_GTApp Field { get; set; }

        public bool IsMandatory { get; set; }

        public bool IsVisible { get; set; }

        public long CompanyId { get; set; }
    }
}
