﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.IProduct;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using FA.MasterDB.SQLDatabase.Mapping;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.ProductRepo
{
    public class ProductDataAccessRepository :
            IProductCategoryRepository,
        IProductRepository
    {
        private readonly MasterDbContext masterDbContext;

        public ProductDataAccessRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        private IQueryable<ProductTable> GetAllCompanyProducts(
            long companyId)
        {
            return masterDbContext.Products.Where(s => s.CompanyId
                == companyId);
        }

        public async Task<Dictionary<long, ProductWithPrimaryCategory>>
            GetProductDicAsync(long companyId)
        {
            return await GetAllCompanyProducts(companyId)
                .Include(s => s.ProductCategory).ThenInclude(s => s.ProductPrimaryCategory)
                .Select(p => ProductMappingHelper.
                CreateModelForProductWithPC(p)).ToDictionaryAsync(p => p.Id, p => p);
        }

        public async Task<Dictionary<long, ProductWithCategories>>
            GetProductCategoryDictionaryAsync(long companyId, List<long> productIds)
        {
            var products = await GetAllCompanyProducts(companyId)
                .Include(s => s.ProductDisplayCategory)
                .Include(s => s.ProductCategory).ThenInclude(s => s.ProductPrimaryCategory)
                .ThenInclude(s => s.ProductDivision)
                .Where(p => productIds.Contains(p.Id)).Select(p =>
                ProductMappingHelper.ProductsWithCategories(p)
            ).ToListAsync();
            return products.ToDictionary(p => p.Id, p => p);
        }
    }
}
