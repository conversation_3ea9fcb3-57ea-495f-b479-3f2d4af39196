﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    [Table("FAEmployeeDayStartRecords")]
    public class EmployeeDayStartRecord
    {
        public long Id { get; set; }

        public long EmployeeId { get; set; }

        public long? JWEmployeeId { get; set; }

        public long? AssignedJWEmployeeId { get; set; }

        public long? ASMId { get; set; }

        public long? RSMId { get; set; }

        public long? ZSMId { get; set; }

        public long? NSMId { get; set; }

        public long? ParentId { get; set; }

        public PortalUserRole UserRole { get; set; }

        [Column("BeatId")]
        public long? SelectedBeatId { get; set; }

        public long? PJPId { get; set; }

        public long? AssignedBeat { get; set; }

        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }

        [Obsolete("Not populating this field but using Server Time as DayStartTime", true)]
        [Column(TypeName = "datetime2")]
        public DateTime? Time { get; set; }

        [Column("DayStartServerTime", TypeName = "datetime2")]
        public DateTime ServerTime { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime LocalDate { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime LocalTime { get; set; }

        [Column("DayEndServerTime", TypeName = "datetime2")]
        public DateTime? DayEndTime { get; set; }

        public DayEndType DayEndType { get; set; }

        public long CompanyId { get; set; }

        public DayStartType DayStartType { get; set; }

        public bool IsInvalid { get; set; }

        [StringLength(2000)]
        public string DayStartTypeReason { get; set; }

        [StringLength(64)]
        public string PlannedDayStartType { get; set; }

        [StringLength(1000)]
        public string DayStartTypeReasonCategory { get; set; }

        public long? LocationId { get; set; }

        [StringLength(40)]
        public string Guid { get; set; }

        public Guid SessionGuid { get; set; }

        public Guid? EmployeeDeviceIdentifier { get; set; }

        public decimal? DayEndLatitude { get; set; }

        public decimal? DayEndLongitude { get; set; }

        public string DayStartLocation { get; set; }

        public string DayEndLocation { get; set; }

        public EmployeeType UserType { get; set; }

        public int? ScheduledCalls { get; set; }

        public long? AssignedRouteId { get; set; }

        public long? SelectedRouteId { get; set; }
    }
}
