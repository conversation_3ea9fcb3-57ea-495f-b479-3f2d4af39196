﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.TaskManagement
{
    [Table("TaskManagementFocusAreas")]
    public class TaskManagementFocusArea
    {
        [Column("Id")]
        public long Id { get; set; }

        [StringLength(64)]
        [Column("Name")]
        public string Name { get; set; }

        [Column("Type")]
        public TaskManagementFocusAreaType? Type { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("EntityId")]
        public long? EntityId { get; set; }

        [Column("IsAutoSequenceRequired")]
        public bool? IsAutoSequenceRequired { get; set; }

        [Column("SequencingType")]
        public SequencingType? SequencingType { get; set; }

        [Column("IsDeactive")]
        public bool? IsDeactive { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("ParentId")]
        public long ParentId { get; set; }

        [Column("ParentType")]
        public ParentType ParentType { get; set; }

        [Column("ProcessingType")]
        public ProcessingType ProcessingType { get; set; }

        public TaskManagementUserFocusArea TaskManagementUserFocusAreas { get; set; }

        [ForeignKey("EntityId")]
        public ProductTagMaster ProductTagMasters { get; set; }

        public ICollection<TaskManagementTask> TaskManagementTasks { get; set; }

        [Column("TaskLevelHierarchy")]
        public ProductLevelHierarchy? TaskLevelHierarchy { get; set; }

        [Column("FocusAreaSequence")]
        public int? FocusAreaSequence { get; set; }
    }
}
