﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Interface;
using FA.MasterDB.Core.Repositories.ILocation;

namespace FA.MasterDB.Core.Service
{
    public class LocationService : ILocationService
    {
        private readonly ILocationRepository _locationRepository;
        private readonly IOutletSegmentationAttributesRepository _outletSegmentationAttributesRepository;
        private readonly IChannelRepository _channelRepository;

        public LocationService(ILocationRepository locationRepository,
            IOutletSegmentationAttributesRepository outletSegmentationAttributesRepository,
            IChannelRepository channelRepository)
        {
            _locationRepository = locationRepository;
            _outletSegmentationAttributesRepository = outletSegmentationAttributesRepository;
            _channelRepository = channelRepository;
        }

        public async Task<LocationWithGeoHierarchy> GetLocationWithSegmentation(long companyId, long locationId)
        {
            var location = await _locationRepository.GetOutletById(companyId, locationId);

            var segment = await _outletSegmentationAttributesRepository.GetOutletSegment(companyId,
                location.Segmentation);

            if (location.ChannelId == 0)
            {
                var channel = await _channelRepository.GetOutletChannelEntityMin(
                    companyId, location.OutletChannel);

                if (channel != null)
                {
                    location.ChannelId = channel.Id;
                }
            }

            if (segment != null)
            {

                location.OutletSegmentationId = segment.Id;
            }

            return location;
        }
    }
}
