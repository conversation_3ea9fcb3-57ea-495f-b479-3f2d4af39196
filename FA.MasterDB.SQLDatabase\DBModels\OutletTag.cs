﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace FA.MasterDB.SQLDatabase.DBModels;

public class OutletTag
{
    public long Id { get; set; }

    public string Name { get; set; }

    public long CompanyId { get; set; }

    public bool IsDeactive { get; set; }

    [Column("IconImage")]
    public string IconImage { get; set; }

    public string TagColor { get; set; }

    public string TextColor { get; set; }

    public bool ShowInApp { get; set; }

    public bool ShowIconwithText { get; set; }
}
