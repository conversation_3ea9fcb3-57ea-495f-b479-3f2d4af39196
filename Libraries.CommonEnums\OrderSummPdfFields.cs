﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums
{
    public enum OrderSummPdfFields
    {
        CompanyLogo = 1,
        CompanyName = 2,
        CompanyGSTIN = 3,
        Address = 4,
        OrderSummary = 5,
        OrderDate = 6,
        DistributorName = 7,
        DistributorNumber = 8,
        DistributorAddress = 9,
        DistributorGSTIN = 10,
        RetailerName = 11,
        RetailerNumber = 12,
        RetailerAddress = 13,
        RetailerGSTIN = 14,
        UserName = 15,
        UserNumber = 16,
        BeatDetails = 17,
        ProductName = 18,
        ProductCode = 19,
        HSNNumber = 20,
        Quantity = 21,
        MRPPerUnit = 22,
        RatePerUnitPTR = 23,
        FreeQuantity = 24,
        DiscountAmount = 25,
        GSTPercent = 26,
        GSTAmount = 27,
        CessAmount = 28,
        TotalValueInclGST = 29,
        TaxableAmount = 30,
        CGST = 31,
        SGST = 32,
        IGST = 33,
        Discount = 34,
        TotalAmountInclTaxes = 35,
        Remarks = 36,
        SuperUnits = 37,
        DistributorTaxId = 38,
        DistributorErpId = 39,
        DistributorAttributeText1 = 40,
        DistributorAttributeText2 = 41,
        DistributorAttributeText3 = 42,
        DistributorAttributeNumber1 = 43,
        DistributorAttributeNumber2 = 44,
        UserErpID = 45,
        VehicleNumber = 46,
        DriverName = 47,
        DriverNumber = 48,
        VehicleErpID = 49,
        EmployeeAttributeText1 = 50,
        EmployeeAttributeText2 = 51,
        EmployeeAttributeNumber1 = 52,
        EmployeeAttributeNumber2 = 53,
        RetailerTaxID = 54,
        RetailerErpID = 55,
        AttributeText1 = 56,
        AttributeText2 = 57,
        AttributeText3 = 58,
        AttributeNumber1 = 59,
        AttributeNumber2 = 60,
        AttributeNumber3 = 61,
        ReturnQuantity = 62,
        FOCValue = 63,
        TaxPercent = 64,
        TaxAmount = 65,
        TotalValueAfterDiscount = 66,
        GrossAmount = 67,
        TotalCessAmount = 68,
        TotalFOCValue = 69,
        TotalBenefits = 70,
        InvoiceNumber = 71,
        SerialNumber = 72,
        CUInvoiceNumber = 73,
        QRCode = 74,
        CompanyTaxID = 75,
        ChassisNumber = 76,
        GST = 77,
        TotalExcise = 78,
        AmountAfterDiscount = 79,
        ExpectedDeliveryDate = 80,
        SalesmanName = 81,
        RetailerSignature = 82,
        TermsAndConditions = 83,
        NetValue = 84,
        GrossTotalCurrencyCode = 85,
        CurrencyConversionFactor = 86,
        OrderRemark = 87,
        OrderDiscountPercent = 88,
        DiscountPercent = 89,
        Salutation = 90
    }
}
