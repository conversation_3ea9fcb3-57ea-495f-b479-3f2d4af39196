﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("F2KLocations")]
    public class Location
    {
        public long Id { get; set; }
        public string ShopName { get; set; }
        public string? GUID { get; set; }
        public long Company { get; set; }
        public string? ErpId { get; set; }
        public string? OwnersName { get; set; }
        public string? OwnersNo { get; set; }
        public string? Address { get; set; }
        public string? SubCity { get; set; }
        public string? MarketName { set; get; }
        public string? City { set; get; }
        public string? State { set; get; }
        public long? BeatId { get; set; }
        public Beat Beat { get; set; }
        public bool IsBlocked { set; get; }
        public string? Email { get; set; }
        public string? PinCode { get; set; }
        public string? GSTIN { get; set; }
        public string? TIN { get; set; }
        public string? PAN { get; set; }
        public string? A<PERSON>har { get; set; }
        public bool GSTRegistered { get; set; }
        public string? FormattedAddress { get; set; }
        public long? ShopTypeId { get; set; }
        public OutletSegmentation Segmentation { get; set; }
        public OutletChannel OutletChannel { get; set; }
        public ShopType? ShopType { get; set; }
        public decimal? Latitude { get; set; }

        public decimal? Longitude { get; set; }
        public string? FSSAINumber { get; set; }
    }
}
