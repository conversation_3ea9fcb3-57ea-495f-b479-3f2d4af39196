﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class DistributorAddress
    {
        public long Id { get; set; }

        public bool IsDeactive { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        [StringLength(6)]
        public string PinCode { get; set; }

        public long CompanyId { get; set; }

        public Company Company { get; set; }

        public long? RegionId { get; set; }

        public Region Region { get; set; }

        public long? EntityId { get; set; }

        public AddressEntityType EntityType { get; set; }

        public string State { get; set; }

        public string City { get; set; }

        public string Address { get; set; }

        public string GSTIn { get; set; }

        public string ManagerName { get; set; }

        public string ManagerEmail { get; set; }

        public string ManagerPhoneNo { get; set; }

        public AddressType AddressType { get; set; }

        public string WareHouseName { get; set; }

        public string WarehouseERPID { get; set; }

        public string ERPID { get; set; }
    }
}
