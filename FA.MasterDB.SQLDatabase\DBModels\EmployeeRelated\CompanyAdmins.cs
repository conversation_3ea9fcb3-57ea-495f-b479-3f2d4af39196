﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class CompanyAdmins
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string PositionInCompany { get; set; }
        public bool? Authenticated { get; set; }
        public long Company { get; set; }
        public DateTime CreatedOn { get; set; }
        public string Role { get; set; }
        public string Email { get; set; }
        public string PhoneNo { get; set; }
        public string EmailId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsDeactive { get; set; }
        public bool IsBillable { get; set; }
        public Guid? LoginGuid { get; set; }
        public Guid Guid { get; set; }
        public Libraries.CommonEnums.PortalUserRole UserRole { get; set; }
        public long? RegionalParentId { get; set; }
    }
}
