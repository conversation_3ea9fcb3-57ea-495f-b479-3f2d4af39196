﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums;

public enum EmployeeTargetOn
{
    None,
    PrimaryCategory,
    Overall,
    Daily
}

public enum EmployeeTargetsCalculationType
{
    Order,
    DispatchAgainstOrder,
    Invoices
}

public enum TargetOn
{
    OverAll = 0,
    PrimaryCategory = 1,
    ProductDivision = 2
}

public enum TargetValueTypeDashboard
{
    Revenue,
    Quantity,
    StandardUnit
}
