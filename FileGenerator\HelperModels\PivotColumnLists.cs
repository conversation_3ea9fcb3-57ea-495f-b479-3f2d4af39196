﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;

namespace FileGenerator.HelperModels
{
    //This is used in flexible reports
    public class CreateExcelProperties
    {
        public DataTable data { get; set; }
        public List<Dictionary<string, object>> dataList { get; set; }
        public List<string> derivedkpis { get; set; }
        public List<string> columnsOrder { get; set; }
        public bool showSubotals { get; set; } // usegrouping

        public DataTable horizonSubgroupData { get; set; }
        public string[] sheetNames { get; set; }

        //for pivoting
        public PivotDetails PivotDetails { get; set; }
        public bool useDoublePivot { get; set; }
        public Dictionary<string, List<string>> doublePivotDictionary { get; set; }
        public List<string> columnsToDeleteFromPivot { get; set; }

        public string defaultGroupColumnName { get; set; }
        public Dictionary<string, string> conditionalFormattingDictionary { get; set; }
        public Dictionary<string, string> sortingDictionaryList { get; set; }
        public Dictionary<string, string> groupDisplayTextDictionary { get; set; }
        public string VerticalTotalColumn { get; set; }

        public string delimiter { get; set; } //"_",
        public bool forceNumberTypeInpivot { get; set; }
        public bool isUsingDMS { get; set; }
        //public List<List<CumulativeReportData>> dmsCumulativeData { get; set; }

        public bool useNomenclatureUpdated { get; set; }
        public Stream stream { get; set; }
    }
    public class CreateExcelPropertiesForStdReport
    {
        public long companyId { get; set; }
        public string sheetName { get; set; }
        public PivotColumn pivotColumn { get; set; }
        public List<Dictionary<string, int>> sortingDictionaryList { get; set; }
        public List<Dictionary<string, int>> PivotSortingDictionaryList { get; set; }
        public Dictionary<string, string> groupDisplayTextDictionary { get; set; }
        public string delimiter { get; set; } = "_";
        public Dictionary<string, List<string>> groupData { get; set; } = null;
        public bool showSubotals { get; set; } = false;
        public bool useNomenclatureUpdated { get; set; } = false;
        public bool showGrandTotal { get; set; } = false;

        public string defaultGroupColumnName { get; set; } = null;
        public Dictionary<string, string> conditionalFormattingDictionary { get; set; } = null;
    }
    public class PivotColumn
    {
        public static string PivotTableName => "Pivot";
        public char PivotColumnSeperator => (ParentColumn != null && SecondaryPivotColumn != null) ? '|' : '+';
        public char PivotLevel2ColumnSeperator => (ParentColumn != null && SecondaryPivotColumn != null) ? '|' : '+';
        public string ParentColumn { get; set; }
        public string SecondaryPivotColumn { get; set; }
        public string[] ValueColumns { get; set; }

        public string ValueColumn
        {
            get => ValueColumns.FirstOrDefault();
            set => ValueColumns = new[] { value };
        }

        public int? Step { get; set; }
        public bool addLexicoSorting { get; set; }
    }
}
