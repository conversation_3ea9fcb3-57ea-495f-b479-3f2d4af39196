﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums;

public enum LMSLeadStatus
{
    ACTIVE = 1,
    JUNK = 2,
    UNQUALIFIED = 3,
    DUPLICATE = 4,
    ARCHIVED = 5
}

public enum LMSPriority
{
    HOT = 1,
    WARM = 2,
    COLD = 3
}

public enum LMSTaskPriority
{
    LOW = 1,
    MEDIUM = 2,
    HIGH = 3
}

public enum LMSActivityType
{
    LeadUpdate = 1,
    StageChange = 2,
    Call = 3,
    Meeting = 4,
    Task = 5,
    Note = 6
}

public enum LMSCustomFieldsEntityType
{
    Lead = 1,
    Account = 2
}

public enum LMSCustomFieldType
{
    Text = 1,
    Number = 2,
    Date = 3,
    Dropdown = 4,
    Checkbox = 5,
    Textarea = 6,
    Email = 7,
    GSTIN = 8,
    PAN = 9,
    FileUpload = 10,
    ImageUpload = 11,
    ImageandGeolocation = 12,
    LocationPicker = 13,
    Signature = 14,
    MultiSelect = 15
}
public enum LMSActivitySource
{
    Web = 1,
    Mobile = 2,
    System = 3 // For automated activities
}

public enum LMSCallType
{
    Inbound = 1,
    Outbound = 2
}

public enum LMSTaskStatus
{
    Scheduled = 1,   /* for calls and meetings*/
    Completed = 2,   /*-- common to all */
    Cancelled = 3,   /*-- common to all */
    NotStarted = 4   /*-- only for tasks */
}
public enum LMSMeetingLocation
{
    InOffice = 1,
    ClientLocation = 2,
    Online = 3
}
public enum LeadStageCategory
{
    Open,
    Working,
    ClosedWon,
    ClosedLost
}
