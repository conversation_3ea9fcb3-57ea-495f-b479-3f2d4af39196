﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class StateWithDistrict
    {
        public long Id { get; set; }

        [ForeignKey("CountryDetail")]
        public string CountryName { get; set; }

        public string StateName { get; set; }

        public string CityName { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }
    }
}
