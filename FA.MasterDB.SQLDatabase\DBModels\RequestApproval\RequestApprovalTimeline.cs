﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.RequestApproval
{
    public class RequestApprovalTimeline
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        [Column("RequestId")]
        public long RequestId { get; set; }

        [Column("RequestType")]
        public ApprovalEngineRequestType RequestType { get; set; }

        [Column("RuleId")]
        public long RuleId { get; set; }

        [Column("RequesterUserId")]
        public long RequesterUserId { get; set; }

        [Column("RequesterPositionId")]
        public long RequesterPositionId { get; set; }

        [Column("RequesterPositionLevel")]
        public PositionCodeLevel RequesterPositionLevel { get; set; }

        [Column("ApproverPositionId")]
        public long ApproverPositionId { get; set; }

        [Column("ApproverUserId")]
        public long ApproverUserId { get; set; }

        [Column("ApproverPositionLevel")]
        public PositionCodeLevel ApproverPositionLevel { get; set; }

        [Column("RequestStatus")]
        public ApprovalEngineRequestStatus RequestStatus { get; set; }

        [Column("Sequence")]
        public int Sequence { get; set; }

        [Column("ActionTakenByUserRole")]
        public int? ActionTakenByUserRole { get; set; }

        [Column("ActionTakenById")]
        public long? ActionTakenById { get; set; }

        [Column("Remarks")]
        public string Remarks { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }
    }
}
