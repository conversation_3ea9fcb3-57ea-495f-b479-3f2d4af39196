﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class LocationMapping
    {
        public static LocationWithGeoHierarchy CreateObject_LocationWithHierarchy(Location location)
        {
            var beat = location.Beat;
            var territory = beat?.Territory;
            var region = territory?.Region;
            var zone = region?.Zone;
            var shopType = location?.ShopType;
            var channel = shopType?.Channel;

            return new LocationWithGeoHierarchy
            {
                Address = location.Address,
                Beat = beat?.Name,
                BeatErpId = beat?.ErpId,
                BeatId = beat?.Id,
                GSTIN = location.GSTIN,
                Id = location.Id,
                TIN = location.TIN,
                OwnersName = location.OwnersName,
                OwnersNo = location.OwnersNo,
                ShopTypeId = location.ShopTypeId,
                Segmentation = location.Segmentation,
                OutletChannel = location.OutletChannel,
                PAN = location.PAN,
                PinCode = location.PinCode,
                Territory = territory?.Name,
                TerritoryId = territory?.Id,
                Region = region?.Name,
                RegionId = region?.Id,
                ShopName = location.ShopName,
                State = location.State,
                Zone = zone?.Name,
                ZoneId = zone?.Id,
                ErpId = location.ErpId,
                EmailId = location.Email,
                ChannelType = channel?.CustomName ?? channel?.DefaultName,
                ChannelId = channel?.Id ?? 0,
                ShopType = shopType?.ShopTypeName,
                City = location.City,
                FSSAINumber = location.FSSAINumber
            };
        }
    }
}
