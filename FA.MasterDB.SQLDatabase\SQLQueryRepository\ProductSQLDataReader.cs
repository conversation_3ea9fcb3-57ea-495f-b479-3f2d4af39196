﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.IProduct;
using FA.MasterDB.SQLDatabase.DBConnection;

namespace FA.MasterDB.SQLDatabase.SQLQueryRepository
{
    public class ProductSQLDataReader : IProductTaxationRepository
    {
        private readonly MasterDbDataReader masterDbSqlDataReader;

        public ProductSQLDataReader(MasterDbDataReader masterDbSqlDataReader)
        {
            this.masterDbSqlDataReader = masterDbSqlDataReader;
        }

        public async Task<List<ProductCessInfoDTO>> GetProductCessInfo(long companyId)
        {

            var data = (await masterDbSqlDataReader.GetModelFromQueryAsync<ProductCessInfoDTO>($@"
                        select p.ID as Id,g.CESS from FACompanyProducts as p 
                        left join CESSCategoryTaxes as g on p.ProductCESSCategoryId=g.CompanyCESSCategoryId 
                        where g.IsEnded=0 and g.CompanyId={companyId}")).ToList();
            return data;

        }

        public async Task<List<ProductTaxInfoDTO>> GetProductTaxInfo(long companyId)
        {
            try
            {
                var data = (await masterDbSqlDataReader.GetModelFromQueryAsync<ProductTaxInfoDTO>($@"
                        select p.ID as Id,g.VAT from FACompanyProducts as p 
                        left join GSTCategoryTaxes as g on p.ProductGSTCategoryId=g.CompanyGSTCategoryId 
                        where g.IsEnded=0 and g.CompanyId={companyId}")).ToList();
                return data;
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync(ex.Message);
                await Console.Error.WriteLineAsync(ex.StackTrace);
                return null;
            }
        }

        public async Task<List<ProductAllTaxInfoDto>> GetProductAllTaxInfo(long companyId)
        {
            try
            {
                var data = (await masterDbSqlDataReader.GetModelFromQueryAsync<ProductAllTaxInfoDto>($@"
                                select p.ID as Id,g.VAT, g.CGST,g.SGST,g.IGST from FACompanyProducts as p 
                                left join GSTCategoryTaxes as g on p.ProductGSTCategoryId=g.CompanyGSTCategoryId  
                        where g.IsEnded=0 and g.CompanyId={companyId}")).ToList();
                return data;
            }
            catch (Exception ex)
            {
                await Console.Error.WriteLineAsync(ex.Message);
                await Console.Error.WriteLineAsync(ex.StackTrace);
                return null;
            }
        }
    }
}
