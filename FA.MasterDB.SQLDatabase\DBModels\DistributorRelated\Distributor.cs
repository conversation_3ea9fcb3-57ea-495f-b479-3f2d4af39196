﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    [Table("FADistributors")]
    public class Distributor
    {
        public long Id { get; set; }
        public string? Name { get; set; }
        public string? ClientSideId { get; set; }
        public string? LocalName { get; set; }
        public string? State { get; set; }

        public string? Address { get; set; }
        public string? PinCode { get; set; }
        public string? GSTIN { get; set; }
        public string? PANNumber { get; set; }
        public long CompanyId { get; set; }
        public long? ParentId { get; set; }
        public StockistType StockistType { get; set; }
        public string? DistributorType { get; set; }
        public bool Deleted { set; get; }
        public Distributor? Parent { get; set; }
        public string? Place { get; set; }
        public string? ContactNo { get; set; }
        public Guid Guid { get; set; }
        public Guid? LoginGuid { get; set; }
        public long? RegionId { get; set; }
        public long? ZoneId { get; set; }
        public string? BankName { get; set; }
        public string? BankAccountNumber { get; set; }
        public string? BankIFSCCode { get; set; }
        public string? FSSAINumber { get; set; }
        public Region? Region { get; set; }
        public string? EmailId { get; set; }

        public long? DistributorChannelId { get; set; }
        public long? DistributorSegmentationId { get; set; }
        [ForeignKey("DistributorSegmentationId")]
        public virtual DistributorSegmentations DistributorSegmentations { get; set; }
        [ForeignKey("DistributorChannelId")]
        public virtual DistributorChannels DistributorChannels { get; set; }
    }
}
