﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.CompanyRelated
{
    public class CompanyAppMeta
    {
        public long Id { get; set; }

        // public DateTime CreatedAt { get; set; }
        // public DateTime LastUpdatedAt { get; set; }
        // public string CreationContext { get; set; }
        public int AppVersionNumber { get; set; }

        public int? MinRequiredAppVersion { get; set; }

        public string AppVariantName { get; set; }

        // public DateTime TimeAdded { get; set; }
        // public DateTime LastUpdated { get; set; }
        public int? Soft_Update { get; set; }

        public string Soft_Update_Msg { get; set; }

        public int? Hard_Update { get; set; }

        public string Hard_Update_Msg { get; set; }

        public AppStages? StageValue { get; set; }

        public string StageMessage { get; set; }
    }
}
