﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Newtonsoft.Json;

namespace FA.MasterDB.SQLDatabase.DBModels.Survey
{
    public class Choice
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("text")]
        public string Text { get; set; }

        public string Value { get; set; }

        [JsonProperty("condition")]
        public string Condition { get; set; }

        [JsonProperty("isSelected")]
        public bool IsSelected { get; set; }

        [JsonIgnore]
        public long QuestionId { get; set; }

        [Json<PERSON>roperty(nameof(Deleted))]
        public bool? Deleted { get; set; }

        [JsonIgnore]
        public Question Question { get; set; }
    }
}
