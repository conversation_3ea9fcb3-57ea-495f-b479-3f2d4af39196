﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;
using Newtonsoft.Json;

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes
{
    [Table("Schemes")]
    public class Scheme
    {
        public long Id { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime CreatedAt { get; set; }

        public Guid Guid { get; set; }

        public string CreationContext { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime LastUpdatedAt { get; set; }

        public bool IsActive { get; set; }

        [Column("IsDeleted")]
        public bool Deleted { get; set; }

        public string Name { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime StartTime { get; set; }

        [Column(TypeName = "datetime2")]
        public DateTime EndTime { get; set; }

        public string StringDescription { get; set; }

        /// <summary>
        /// Gets or sets is In Percentage of Purchase Amount or Article Value as per the Discount Type.
        /// </summary>
        [Obsolete("Use Slabs", true)]
        public decimal DiscountValue { get; set; }

        [Obsolete("Use Slabs with Payout Type", true)]
        public DiscountType DiscountType { get; set; }

        public DiscountBlock DiscountOn { get; set; }

        public string DiscountBlockArray { get; set; }

        [Obsolete("Use Slabs", true)]
        public int MinQtyConstraint { get; set; }

        [Obsolete("Use Slabs", true)]
        public decimal MinAmountConstraint { get; set; }

        // [Obsolete]
        // public double MinConstraintValue { set; get; }
        public ConstraintType ConstraintType { get; set; }

        [Obsolete("Use Slabs", true)]
        public string SchemeOffer { get; set; }

        public string OutletConstraints { get; set; }

        public string DistributorConstraints { get; set; }

        public SchemeCategorization Category { get; set; }

        public PayoutType PayoutType { get; set; }

        public bool IsNew { get; set; }

        public PayoutCalculationType PayoutCalculationType { get; set; }

        public long CompanyId { get; set; }

        public string DistributorIdBlock { get; set; }

        public long? ZoneId { get; set; }

        public long? RegionId { get; set; }

        public string ErpId { get; set; }

        public DiscountBlock? PayoutOn { get; set; }

        public string SelectedProductBlockArray { get; set; }

        public SchemeType? SchemeType { get; set; }

        public SchemeSubType? SchemeSubType { get; set; }

        public virtual ICollection<SchemeSlab> SchemeSlabs { get; set; }

        public virtual Distributor Distributor { get; }

        public virtual Region Region { get; }

        public bool? MandatoryScheme { get; set; }

        public PayoutIn? PayoutIn { get; set; }

        public bool IsQPS { get; set; }

        public int? IsSchemeApproved { get; set; }

        public int? SchemeStep { get; set; }

        public bool? IsIndividual { get; set; }

        public string FilterConstraints { get; set; }

        public bool? IsTwoQualifierScheme { get; set; }

        public string States { get; set; }
        public bool IsMRPBilling { get; set; }

        public SchemeDisbursementMethods? SchemeDisbursementMethod { get; set; }

        public string UserConstraints { get; set; }

        [NotMapped]
        public SchemeUserConstraints SchemeUserConstraintsJson => !string.IsNullOrEmpty(UserConstraints) ?
            JsonConvert.DeserializeObject<SchemeUserConstraints>(UserConstraints) : null;

        public bool? IsInclusive { get; set; }

        public SchemeCappingType? CappingType { get; set; }

        public double? CappingValue { get; set; }

        public long? AlternateScheme { get; set; }

        public bool? IsPreferred { get; set; }
    }
}
