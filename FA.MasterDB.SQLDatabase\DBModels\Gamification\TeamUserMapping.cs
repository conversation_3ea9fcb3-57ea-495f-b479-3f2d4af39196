﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.Gamification
{
    public class TeamUserMapping
    {
        public long Id { get; set; }

        [ForeignKey("Team")]
        public long TeamId { get; set; }

        [ForeignKey("ClientEmployee")]
        public long TeamPlayerId { get; set; }

        public bool IsActive { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public ClientEmployee ClientEmployee { get; set; }

        public Team Team { get; set; }
    }
}
