﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    [Table("ProductDisplayCategories")]
    public class ProductDisplayCategories
    {
        public long Id { get; set; }
        public DateTime LastupdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? CreationContext { get; set; }
        public bool IsDeactive { get; set; }
        public bool Deleted { get; set; }
        public string Name { get; set; }
        public long CompanyId { get; set; }
        public long ProductSecondaryCategoryId { get; set; }
        public bool? IsAssorted { get; set; }
    }
}
