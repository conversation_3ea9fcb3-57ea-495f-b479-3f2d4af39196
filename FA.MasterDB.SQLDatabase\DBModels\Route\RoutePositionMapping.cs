﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.Position;

namespace FA.MasterDB.SQLDatabase.DBModels.Route
{
    [Table("RoutePositionMappings")]
    public class RoutePositionMapping
    {
        public long Id { get; set; }

        public long CompanyId { get; set; }

        public bool Deleted { get; set; }

        [ForeignKey("Routes")]
        public long RouteId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        [ForeignKey("PositionCode")]
        public long PositionCodeId { get; set; }

        public virtual PositionCode PositionCode { get; set; }

        public virtual RouteClass Routes { get; set; }
    }
}
