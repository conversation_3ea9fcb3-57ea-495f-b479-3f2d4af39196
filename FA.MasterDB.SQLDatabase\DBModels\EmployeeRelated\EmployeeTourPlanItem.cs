﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.Route;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;

public class EmployeeTourPlanItem
{
    public long Id { get; set; }

    public long EmployeeTourPlanId { get; set; }

    public DateTime ItemDate { get; set; }

    public long? BeatId { get; set; }

    public long? RouteId { get; set; }

    public virtual LocationBeat Beat { get; set; }

    public string ReasonCategory { get; set; }

    public string Reason { get; set; }

    public long? JWFieldUserId { get; set; }

    public virtual RouteClass Route { get; set; }

    public virtual ClientEmployee JWFieldUser { get; set; }

    public EmployeeTourPlan EmployeeTourPlan { get; set; }

    public long CompanyId { get; set; }

    public virtual ICollection<EmployeeTourPlanItemSecondary> EmployeeTourPlanItemsSecondary { get; set; }

    public long? DistributorId { get; set; }

    public long? JWFieldUserPositionId { get; set; }
}
