﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace FA.MasterDB.SQLDatabase.DBModels.Survey;

public class Question
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("displayOrder")]
    public int DisplayOrder { get; set; }

    [JsonIgnore]
    public bool Deactivated { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    [JsonProperty("questionType")]
    public SurveyQuestionType QuestionType { get; set; }

    [JsonProperty("required")]
    public bool Required { get; set; }

    [JsonProperty("validationRule")]
    public string ValidationRule { get; set; }

    [JsonProperty("validationErrorMessage")]
    public string ValidationErrorMessage { get; set; }

    [JsonProperty("hint")]
    public string Hint { get; set; }

    [JsonConverter(typeof(StringEnumConverter))]
    [JsonProperty("displayWidth")]
    public SurveyDisplayWidth DisplayWidth { get; set; }

    [JsonProperty("defaultValue")]
    public string DefaultValue { get; set; }

    [JsonProperty("choices")]
    public List<Choice> Choices { get; set; }

    [JsonIgnore]
    public long QuestionGroupId { get; set; }

    [JsonProperty("outletMetadata")]
    public string OutletMetadata { get; set; }

    [JsonProperty(nameof(Deleted))]
    public bool? Deleted { get; set; }

    [JsonIgnore]
    public QuestionGroup QuestionGroup { get; set; }
}
