﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;

namespace Libraries.CommonEnums.Helpers;

public static class UserRoleExtensions
{
    public static EmployeeRank ToEmployeeRank(this PortalUserRole userRole)
    {
        switch (userRole)
        {
            case PortalUserRole.GlobalSalesManager:
                return EmployeeRank.GSM;

            case PortalUserRole.NationalSalesManager:
                return EmployeeRank.NSM;

            case PortalUserRole.ZonalSalesManager:
                return EmployeeRank.ZSM;

            case PortalUserRole.RegionalSalesManager:
                return EmployeeRank.RSM;

            case PortalUserRole.AreaSalesManager:
                return EmployeeRank.ASM;

            case PortalUserRole.ClientEmployee:
                return EmployeeRank.ESM;

            default:
                throw new Exception($"Role {userRole} can't be converted to Rank!");
        }
    }

    public static string ToUserRoleString(this PortalUserRole userRole)
    {
        switch (userRole)
        {
            case PortalUserRole.GlobalSalesManager:
                return "GSM";

            case PortalUserRole.NationalSalesManager:
                return "NSM";

            case PortalUserRole.ZonalSalesManager:
                return "ZSM";

            case PortalUserRole.RegionalSalesManager:
                return "RSM";

            case PortalUserRole.AreaSalesManager:
                return "ASM";

            case PortalUserRole.ClientEmployee:
                return "ESM";

            default:
                return "";
        }
    }

    public static PortalUserRole ToUserRole(this EmployeeRank employeeRank)
    {
        switch (employeeRank)
        {
            case EmployeeRank.ESM:
                return PortalUserRole.ClientEmployee;

            case EmployeeRank.ASM:
                return PortalUserRole.AreaSalesManager;

            case EmployeeRank.RSM:
                return PortalUserRole.RegionalSalesManager;

            case EmployeeRank.ZSM:
                return PortalUserRole.ZonalSalesManager;

            case EmployeeRank.NSM:
                return PortalUserRole.NationalSalesManager;

            case EmployeeRank.GSM:
                return PortalUserRole.GlobalSalesManager;

            default:
                return PortalUserRole.Unknown;
        }
    }

    public static POApprovalHierarchyLevel ToPOApprovalHierarchyLevel(this PortalUserRole userRole)
    {
        switch (userRole)
        {
            case PortalUserRole.GlobalSalesManager:
                return POApprovalHierarchyLevel.Level6;

            case PortalUserRole.NationalSalesManager:
                return POApprovalHierarchyLevel.Level5;

            case PortalUserRole.ZonalSalesManager:
                return POApprovalHierarchyLevel.Level4;

            case PortalUserRole.RegionalSalesManager:
                return POApprovalHierarchyLevel.Level3;

            case PortalUserRole.AreaSalesManager:
                return POApprovalHierarchyLevel.Level2;

            default:
                return POApprovalHierarchyLevel.None;
        }
    }
}
