﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class ClaimsConfiguration
    {
        public long Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public Guid Guid { get; set; }
        public string CreationContext { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public long CompanyId { get; set; }
        public ClaimType ClaimType { get; set; }
        public EditingType Editing { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsAdditionalConstraint { get; set; }
        public string ClaimConstraint { get; set; }
        public bool IsClaimReason { get; set; }
        public string ClaimReason { get; set; }
    }
}
