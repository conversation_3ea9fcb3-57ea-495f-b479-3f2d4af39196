﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository
{
    public class VanMasterRepository : IVanMasterRepository
    {
        private readonly MasterDbContext masterDbContext;

        public VanMasterRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        public async Task<string> GetVanName(long vanId)
        {
            var vanName = await masterDbContext.VanMaster.Where(s => s.Id == vanId).
                FirstOrDefaultAsync();

            return vanName?.VanName ?? "";
        }
    }
}
