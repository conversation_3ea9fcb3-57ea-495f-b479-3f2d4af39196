﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.ProductRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class ProductMappingHelper
    {
        public static ProductWithPrimaryCategory CreateModelForProductWithPC(ProductTable productTable)
        {
            var productCategory = productTable.ProductCategory;
            var productPrimaryCategory = productCategory.ProductPrimaryCategory;

            return new ProductWithPrimaryCategory
            {
                Id = productTable.Id,
                Name = productTable.Name,
                PrimaryCategory = productPrimaryCategory.Name,
                PrimaryCategoryId = productPrimaryCategory.Id
            };
        }

        public static ProductWithCategories ProductsWithCategories(ProductTable productTable)
        {
            var productCategory = productTable.ProductCategory;
            var productPrimaryCategory = productCategory.ProductPrimaryCategory;

            return new ProductWithCategories
            {
                HSNCode = productTable.HSNCode,
                Id = productTable.Id,
                Name = productTable.Name,
                PrimaryCategory = productPrimaryCategory.Name,
                PrimaryCategoryId = productPrimaryCategory.Id,
                ProductDivision = productPrimaryCategory.ProductDivision.Name,
                ProductDivisionId = productPrimaryCategory?.ProductDivisionId,
                SecondaryCategory = productCategory.Name,
                SecondaryCategoryId = productTable.ProductCategoryId,
                Unit = productTable.Unit,
                StdUnit = productPrimaryCategory.StandardUnit,
                UnitWeight = productTable.UnitWeight,
                NetWeight = productTable.NetWeight,
                GrossWeight = productTable.GrossWeight,
                ErpCode = productTable.ErpCode,
                IsSaleable = productTable.IsSaleable,
                StandardUnitConversionFactor = productTable.StandardUnitConversionFactor,
                SuperUnitConversionFactor = productTable.SuperUnitConversionFactor,
                VariantName = productTable.VariantName,
                MRPNumeric = productTable.MRPNumeric,
                Price = productTable.Price,
                ProductDisplayCategoryId = productTable.ProductDisplayCategoryId,
                DisplayCategory = productTable.ProductDisplayCategory?.Name,
                Category1 = productTable.Category1,
                Product_AttributeText1 = productTable.Product_AttributeText1,
                Color = productTable.ColorName,
                Description = productTable.Description,
                Product_AttributeText2 = productTable.Product_AttributeText2,
                ProductGroupId = productTable.ProductGroupId,
                AdditionalUnit = productTable.AdditionalUnit,
                AdditionalUnitConversionFactor = productTable.AdditionalUnitConversionFactor,
            };
        }
    }
}
