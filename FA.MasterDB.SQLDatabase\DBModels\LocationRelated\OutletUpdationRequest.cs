﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

public class OutletUpdationRequest
{
    public long Id { get; set; }

    public string Address { get; set; }

    public string OwnersName { get; set; }

    public string OwnersNo { get; set; }

    public string ShopType { get; set; }

    public string GSTIN { get; set; }

    public string Email { get; set; }

    public string PinCode { get; set; }

    public DateTime AddedOn { get; set; }

    public long LocationId { get; set; }

    public long? PositionCodeId { get; set; }

    [StringLength(500)]
    public string ShopName { get; set; }

    [StringLength(100)]
    public string MarketName { get; set; }

    [StringLength(50)]
    public string City { get; set; }

    [StringLength(50)]
    public string State { get; set; }

    [StringLength(50)]
    public string PlaceOfDelivery { get; set; }

    [StringLength(50)]
    public string TIN { get; set; }

    [StringLength(10)]
    public string PAN { get; set; }

    [StringLength(12)]
    public string Aadhar { get; set; }

    public long? ShopTypeId { get; set; }

    public OutletSegmentation? Segmentation { get; set; }

    [Column("Approved")]
    public bool Reviewed { get; set; }

    public OutletChannel? OutletChannel { get; set; }

    public bool? Disapproved { get; set; }

    [Column("ApprovedOn")]
    public DateTime? ReviewedOn { get; set; }

    [Column("ApprovedBy")]
    public long? ReviewedBy { get; set; }

    [Column("ApprovedByUserRole")]
    public PortalUserRole? ReviewedByUserRole { get; set; }

    public Guid? ImageId { get; set; }

    [NotMapped]
    public string Image { get; set; }

    [Column("Employee")]
    public long EmployeeId { get; set; }

    public string RequestId { get; set; }

    public bool GSTRegistered { get; set; }

    [StringLength(18)]
    public string BankAccountNumber { get; set; }

    public string AccountHoldersName { get; set; }

    [StringLength(11)]
    public string IFSCCode { get; set; }

    public string AttributeText1 { get; set; }

    public string AttributeText2 { get; set; }

    public string AttributeText3 { get; set; }

    public string RejectionReason { get; set; }

    public string AttributeText4 { get; set; }

    public double? AttributeNumber1 { get; set; }

    public double? AttributeNumber2 { get; set; }

    public double? AttributeNumber3 { get; set; }

    public double? AttributeNumber4 { get; set; }

    public bool? AttributeBoolean1 { get; set; }

    public bool? AttributeBoolean2 { get; set; }

    public DateTime? AttributeDate1 { get; set; }

    public DateTime? AttributeDate2 { get; set; }

    public string AttributeImage1 { get; set; }

    public string AttributeImage2 { get; set; }

    public string AttributeImage3 { get; set; }

    public long CompanyId { get; set; }

    [Column(TypeName = "decimal(18,10)")]
    public decimal? Latitude { get; set; }

    [Column(TypeName = "decimal(18,10)")]
    public decimal? Longitude { get; set; }

    public double? GeoAccuracy { get; set; }

    [StringLength(1000)]
    public string FormattedAddress { get; set; }

    public long? GeographicalMappingId { get; set; }

    public string FSSAINumber { get; set; }

    public DateTime? FSSAIExpiryDate { get; set; }

    public string RouteIds { get; set; }

    public IsKYCEnum? IsKYCEnum { get; set; }

    public long? BeatId { get; set; }
}
