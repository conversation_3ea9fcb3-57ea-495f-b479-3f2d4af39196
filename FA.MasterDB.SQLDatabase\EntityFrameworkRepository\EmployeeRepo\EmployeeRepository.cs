﻿// Copyright (c) FieldAssist. All Rights Reserved.

//using System.Data.Entity;
using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.IEmployee;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated;
using FA.MasterDB.SQLDatabase.Mapping;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.EmployeeRepo
{
    public class EmployeeRepository : IEmployeeRepository
    {
        private readonly MasterDbContext masterDbContext;

        public EmployeeRepository(MasterDbContext masterDbContext)
        {
            this.masterDbContext = masterDbContext;
        }

        public async Task<EmployeeWithParent> GetEmployeeWithParent(long companyId, long userId)
        {
            return await GetEmployeeData(companyId, userId)
                .Select(e => EmployeeMapping.CreateObject_CreateEmployeeWithParent(e))
                .FirstOrDefaultAsync();
        }

        public async Task<string> GetEmployeeName(long companyId, long employeeId)
        {
            return await GetEmployeeData(companyId, employeeId)
                .Select(e => e.Name)
                .FirstOrDefaultAsync();
        }

        public async Task<EmployeeEntity> GetEmployee(long companyId, long employeeId)
        {
            return await GetEmployeeData(companyId, employeeId)
                .Select(s => EmployeeMapping.CreateEmployeeObject(s))
                .FirstOrDefaultAsync();
        }

        private IQueryable<ClientEmployee> GetEmployeeData(long companyId, long userId)
        {
            return ApplyCompanyFilter(companyId)
                .Where(e => e.Id == userId);
        }

        public IQueryable<ClientEmployee> ApplyCompanyFilter(long companyId)
        {
            return masterDbContext.ClientEmployees
                .Where(s => s.Company == companyId);
        }
    }
}
