﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace FA.MasterDB.SQLDatabase.DBModels.Route;

public class RoutePlanRequestItem
{
    public long Id { get; set; }

    public long RoutePlanRequestId { get; set; }

    public long? RouteId { get; set; }

    public int DayNumber { get; set; }

    public long? JWFieldUserId { get; set; }

    [StringLength(1024)]
    public string Reason { get; set; }

    [StringLength(256)]
    public string ReasonCategory { get; set; }

    public RoutePlanRequest RoutePlanRequest { get; set; }

    public RouteClass Route { get; set; }
}
