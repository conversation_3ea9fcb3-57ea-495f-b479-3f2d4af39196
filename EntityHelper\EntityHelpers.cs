﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System;
using Libraries.CommonEnums;

namespace EntityHelper;

public interface IAuditedEntity : ICreatedEntity, IUpdatableEntity
{
}

public interface IBillable : IEntity
{
    long CompanyId { get; }
    Guid Guid { get; }
    bool IsBillable { get; set; }
    string Name { get; }
    PortalUserRole UserRole { get; }
}

public interface ICompanyEntity : IEntity
{
    long CompanyId { get; }
}

public interface ICreatedEntity : IEntity
{
    DateTime CreatedAt { get; set; }
    string CreationContext { get; set; }
}

public interface ICreatedTransactionEntity : IEntity
{
    DateTime CreatedAt { get; set; }
}

public interface ITransactionEntity
{
    DateTime CreatedAt { get; set; }
    Guid SessionId { get; set; }
}

public interface IDeactivatable
{
    bool IsDeactive { get; set; }
}

public interface IDeletable
{
    bool Deleted { get; set; }
}

public interface IDeviceEntity : IEntity
{
    DateTime DeviceTime { get; set; }
    DateTime ServerTime { get; set; }
}

public interface IEntity
{
    long Id { get; }
}

public interface IERPEntity
{
    string ErpId { get; set; }
    long Id { get; }
}
public interface IERPEntityByCode
{
    string ErpCode { get; set; }
    long Id { get; }
}

public interface IUpdatableEntity : IEntity
{
    DateTime LastUpdatedAt { get; set; }
}
