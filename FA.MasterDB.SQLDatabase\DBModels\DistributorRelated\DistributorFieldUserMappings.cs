﻿// Copyright (c) FieldAssist. All Rights Reserved.

//using System;
//using AppApis.Core.Models;
//using AppApis.Core.Models.MasterDbModels;

//namespace AppApis.DbStorage.MasterDbModels
//{
//    public class DistributorFieldUserMapping
//    {
//        public long Id { get; set; }

//        public long DistributorId { get; set; }

//        public long FieldUserId { get; set; }

//        public long CompanyId { get; set; }

//        public bool Deleted { get; set; }

//        public DateTime LastUpdatedAt { get; set; }

//        public DateTime CreatedAt { get; set; }

//        public string CreationContext { get; set; }

//        public Distributor Distributors { get; set; }

//        public Employee FieldUser { get; set; }
//    }
//}
