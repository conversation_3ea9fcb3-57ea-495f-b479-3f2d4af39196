﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes
{
    public class UserWiseSchemeBudget
    {
        public long Id { get; set; }

        public long SchemeId { get; set; }

        public long UserId { get; set; }

        public double Budget { get; set; }

        public long CompanyId { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public bool IsDeleted { get; set; }

        public double? RemainingBudget { get; set; }
    }
}
