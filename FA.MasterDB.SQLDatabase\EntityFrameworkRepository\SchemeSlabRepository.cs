﻿// Copyright (c) FieldAssist. All Rights Reserved.

using AutoMapper;
using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.Repositories;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository
{

    public class SchemeSlabRepository : ISchemeSlabRepository
    {
        private readonly MasterDbContext _masterDbContext;
        private readonly IMapper _mapper;

        public SchemeSlabRepository(MasterDbContext masterDbContext,
            IMapper mapper)
        {
            _masterDbContext = masterDbContext;
            _mapper = mapper;
        }

        public async Task<List<SchemeSlabBase>> Get(List<long> slabIds)
        {
            // Fetch the data from the database first
            var data = await _masterDbContext.SchemeSlabs
                .Where(s => slabIds.Contains(s.Id))
                .Include(s => s.Scheme)
                .ToListAsync();

            // Perform the mapping after fetching the data
            var mappedData = data.Select(s => _mapper.Map<SchemeSlabBase>(s)).ToList();

            return mappedData;
        }
    }
}
