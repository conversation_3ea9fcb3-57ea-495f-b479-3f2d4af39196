﻿// Copyright (c) FieldAssist. All Rights Reserved.

using NpgsqlTypes;

namespace FA.MasterDB.Core.Enum
{
    public enum PayoutType
    {
        [PgName("FOC")]
        FOC = 1,
        [PgName("Article")]
        Article = 2,
        [PgName("Discount")]
        Discount = 3,
        [PgName("Product")]
        Product = 4,
        [PgName("TopUpDiscount")]
        TopUpDiscount = 5,
        [PgName("PerUnitDiscount")]
        PerUnitDiscount = 6,
        [PgName("Basket")]
        Basket = 7,
        [PgName("FixedValueDiscount")]
        FixedValueDiscount = 8,
        [PgName("CouponScheme")]
        CouponScheme = 10,
        [PgName("PercentageDiscountOnMRP")]
        PercentageDiscountOnMRP = 11,
        [PgName("ProductSchemeNew")]
        ProductSchemeNew = 15
    }
}
