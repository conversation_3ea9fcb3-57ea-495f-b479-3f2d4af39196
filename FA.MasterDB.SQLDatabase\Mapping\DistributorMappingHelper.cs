﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.DistributorRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class DistributorMappingHelper
    {
        public static DistributorWithGeography
            CreateObject_DistributorWithHierarchy(
            Distributor distributor)
        {
            return new DistributorWithGeography
            {
                Address = distributor.Address,
                GSTIN = distributor.GSTIN,
                Id = distributor.Id,
                Name = distributor.Name,
                PANNumber = distributor.PANNumber,
                ContactNo = distributor.ContactNo,
                PinCode = distributor.PinCode,
                State = distributor.State,
                SellerType = distributor.StockistType.ToString(),
                StockistType = distributor.StockistType,
                Region = distributor.Region?.Name ?? string.Empty,
                RegionId = distributor.RegionId,
                Zone = distributor.Region?.Zone?.Name ?? string.Empty,
                ZoneId = distributor.Region?.ZoneId ?? distributor.ZoneId,
                ParentId = distributor.ParentId,
                ClientSideId = distributor.ClientSideId,
                LoginGuid = distributor.LoginGuid,
                Place = distributor.Place,
                BankAccountNumber = distributor.BankAccountNumber,
                BankIFSCCode = distributor.BankIFSCCode,
                BankName = distributor.BankName,
                FSSAINumber = distributor.FSSAINumber,
                UserRole = PortalUserRole.Distributor,
                CompanyId = distributor.CompanyId,
                DistributorChannelId = distributor.DistributorChannelId,
                DistributorSegmentationId = distributor.DistributorSegmentationId,
                DistributorType = distributor.DistributorType,
                EmailId = distributor.EmailId,
                Guid = distributor.Guid,
                LocalName = distributor.LocalName,
                DistributorChannel = distributor.DistributorChannels?.Name,
                DistributorSegmentation = distributor.DistributorSegmentations?.Name,
            };
        }
    }
}
