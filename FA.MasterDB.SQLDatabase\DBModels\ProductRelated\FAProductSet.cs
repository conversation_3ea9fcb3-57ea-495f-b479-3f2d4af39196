﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    public class FAProductSet
    {
        public long Id { get; set; }

        public long DistributorId { get; set; }

        public long CompanyId { get; set; }

        [StringLength(100)]
        public string BasketConfiguration { get; set; }

        [StringLength(50)]
        public string Size { get; set; }

        public int? Quantity { get; set; }

        public bool IsDeleted { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
