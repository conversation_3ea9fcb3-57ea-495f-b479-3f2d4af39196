﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace FA.MasterDB.SQLDatabase.DBModels.ProductRelated
{
    public class FAProductBasket
    {
        public long Id { get; set; }

        public string BasketName { get; set; }

        public string BasketERPID { get; set; }

        public string BasketColour { get; set; }

        public string BasketConfiguration { get; set; }

        public long ProductDisplayCategoryID { get; set; }

        public string CreationContext { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public long CompanyID { get; set; }

        public bool IsDeleted { get; set; }
    }
}
