﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;

namespace FA.MasterDB.Core.Repositories.IDistributor
{
    public interface IDistributorRepository
    {
        Task<List<DistributorWithGeography>> GetDistributors(long companyId);
        Task<DistributorWithGeography>
            GetDistributorById(long companyId, long distributorId);
        Task<Dictionary<long, string>> GetDistributorEmailId(long companyId,
            List<long> distributorIds);
        Task<Dictionary<long, long?>> GetSuperStockistForSubStockist(long companyId,
            List<long> distributorIds);

        Task<string> GetWarehouseStateAsync(long companyId, string warehouseErpId);
    }
}
