﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class VanMaster
    {
        public long Id { get; set; }
        public string VanName { get; set; }
        public string VanRegistrationNumber { get; set; }
        public string ChassisNumber { get; set; }
        public double VanCapacity { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        public string CreationContext { get; set; }
        [Column("IsDeleted")]
        public bool Deleted { get; set; }
        public string VanInvoicePrefix { get; set; }
    }
}
