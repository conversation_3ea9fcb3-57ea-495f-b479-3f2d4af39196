﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.ITertiary;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.TertiaryRepo
{
    public class TertiaryDataAccessRepository : ITertiaryRepository
    {
        private readonly MasterDbContext _masterDbContext;

        public TertiaryDataAccessRepository(MasterDbContext masterDbContext)
        {
            _masterDbContext = masterDbContext;
        }

        public async Task<TertiaryMinModel> GetTertiaryMinAsync(long id, long companyId)
        {
            return await _masterDbContext.TertiaryEntity
                                         .Where(t => t.Id == id && t.CompanyId == companyId && !t.IsDeleted && !t.IsDeactive)
                                         .Select(t => Mapping.TertiaryMappingHelper.GetTertiaryMin(t))
                                         .FirstOrDefaultAsync();
        }
    }
}
