﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.Interfaces;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class LocationBeat : IDeactivatable
    {
        public long Id { get; set; }

        // public bool Deleted { get; set; }
        public bool IsDeactive { get; set; }

        public string Name { get; set; }

        [ForeignKey("Territory")]
        public long? TerritoryId { get; set; }

        public virtual Territory Territory { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public ICollection<LocationDB> Locations { get; set; }

        public long Company { get; set; }

        public string ErpId { get; set; }
    }
}
