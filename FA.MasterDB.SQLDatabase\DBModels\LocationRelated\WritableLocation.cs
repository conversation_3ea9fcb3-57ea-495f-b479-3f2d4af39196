﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.Interfaces;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    public class WritableLocation : LocationDB, ICreatedEntity, IUpdatedEntity
    {
        public new DateTime CreatedAt { get; set; }

        public long Company { get; set; }

        public ShopAdditionType? AdditionType { get; set; }

        public DateTime LastUpdatedAt { get; set; }

        public string CreationContext { get; set; }

        public long CodeId { get; set; }

        public new long? ShopTypeId { get; set; }

        public new long? GeographicalMappingId { get; set; }
    }
}
