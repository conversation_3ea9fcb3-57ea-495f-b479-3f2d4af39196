﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.Core.BaseModels
{

    public class SchemeSlabBase
    {
        public long Id { get; set; }

        public double Constraint { get; set; }

        public double Payout { get; set; }

        public int Priority { get; set; }

        public string PayoutDescription { get; set; }

        public long? ProductId { get; set; }

        public long SchemeId { get; set; }

        public QualifierPayoutType? QualifierPayoutType { get; set; }

        public double? BasketInvoicePayout { get; set; }

        public double? MaxBasketPayout { get; set; }

        public ConstraintType? BasketConstraintType { get; set; }

        public double? NominalPrice { get; set; }

        public string BatchMasterNumber { get; set; }

        public bool MultipleProductPayout { get; set; }

        public long? GroupId { get; set; }

        public string PayoutProductList { get; set; }
        public SchemeEntity Scheme { get; set; }
    }
}
