﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class ShopTypeMapping
    {
        public static ShopTypesDTO
            CreateObject_ShopTypeDTO(
            ShopType s)
        {
            return new ShopTypesDTO()
            {
                ShopTypeName = s.ShopTypeName,
                SkipOTPVerification = s.SkipOTPVerification,
                ChannelId = s.ChannelId,
                CompanyId = s.CompanyId,
                CreatedAt = s.CreatedAt,
                ErpId = s.ErpId,
            };
        }
    }
}
