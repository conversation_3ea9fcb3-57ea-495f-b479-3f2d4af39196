﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="EPPlus" Version="7.4.2" />
    <PackageReference Include="FastMember" Version="1.5.0" />
    <PackageReference Include="itext7" Version="9.2.0" />
    <PackageReference Include="itext7.bouncy-castle-adapter" Version="9.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Libraries.CommonEnums\Libraries.CommonEnums.csproj" />
    <ProjectReference Include="..\Library.CommonHelpers\Library.CommonHelpers.csproj" />
    <ProjectReference Include="..\Library.DateTimeHelpers\Library.DateTimeHelpers.csproj" />
    <ProjectReference Include="..\Library.StringHelpers\Library.StringHelpers.csproj" />
  </ItemGroup>
</Project>