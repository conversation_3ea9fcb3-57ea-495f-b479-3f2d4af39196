﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories.IDistributor;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.DistributorRepo
{
    public class DistributorBeatMappingRepository : IDistributorBeatMappingRepository
    {
        private readonly MasterDbContext dbContext;

        public DistributorBeatMappingRepository(MasterDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<List<long>> GetBeatsForSuperStockists(long companyId, long distId)
        {
            return await dbContext.DistributorBeatMappings
                .Where(d => d.CompanyId == companyId && !d.Deleted && dbContext.Distributors.Any(s => s.ParentId == distId && s.Id == d.DistributorId))
                .Select(s => s.BeatId)
                .ToListAsync();
        }

        public async Task<List<long>> GetBeatIdByDistributorId(long companyId, long distributorId)
        {
            return await dbContext.DistributorBeatMappings
                .Where(d => d.CompanyId == companyId && !d.Deleted && d.DistributorId == distributorId)
                .Select(d => d.BeatId)
                .ToListAsync();
        }
    }
}
