﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.EmployeeRelated
{
    public class FAEmployeeBeatMappings
    {
        public long Id { get; set; }
        public long EmployeeId { get; set; }
        public long BeatId { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeleted { get; set; }
        public ClientEmployee Employee { get; set; }
        public Beat Beat { get; set; }

        public DateTime TimeAdded { get; set; }

        public string CreationContext { get; set; }
    }
}
