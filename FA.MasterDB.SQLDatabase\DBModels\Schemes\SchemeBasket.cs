﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.Schemes;

[Table("SchemeBuckets")]
public class SchemeBasket
{
    [Column("Id")]
    public long Id { get; set; }

    [Column("CreatedAt")]
    public DateTime CreatedAt { get; set; }

    [Column("CreationContext")]
    public string CreationContext { get; set; }

    [Column("LastUpdatedAt")]
    public DateTime LastUpdatedAt { get; set; }

    [Column("Deleted")]
    public bool Deleted { get; set; }

    [Column("ErpId")]
    public string ErpId { get; set; }

    [Column("Name")]
    public string Name { get; set; }

    [Column("ProductIds")]
    public string ProductIds { get; set; }

    [Column("CompanyId")]
    public long CompanyId { get; set; }

    [Column("EntityType")]
    public SchemeBasketEntityType? EntityType { get; set; }

    [Column("EntityValue")]
    public string EntityValue { get; set; }
}
