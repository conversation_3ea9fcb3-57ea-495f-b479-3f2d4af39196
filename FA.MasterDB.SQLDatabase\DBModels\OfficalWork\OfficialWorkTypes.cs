﻿// Copyright (c) FieldAssist. All Rights Reserved.

// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.OfficalWork;

public class OfficialWorkTypes
{
    public long Id { get; set; }

    public long CompanyId { get; set; }

    public FAEventType Enum { get; set; }

    public string CustomName { get; set; }

    public bool IsInvalid { get; set; }

    public DateTime CreatedAt { get; set; }

    public string CreationContext { get; set; }

    public DateTime LastUpdatedAt { get; set; }
}
