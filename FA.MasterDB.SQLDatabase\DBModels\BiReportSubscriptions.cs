﻿// Copyright (c) FieldAssist. All Rights Reserved.

using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    public class BiReportSubscriptions
    {
        public long Id { get; set; }

        public string Name { set; get; }

        public Guid BIReportId { get; set; }

        public string BIReportName { get; set; }

        public PortalUserRole PortalUserRole { get; set; }

        public bool IsDeactive { get; set; }

        public long CompanyId { get; set; }

        public bool isPinned { get; set; }

        public SubscriptionType SubscriptionType { get; set; }

        public long UserId { get; set; }

        public DateTime LastUpdatedAt { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreationContext { get; set; }
        public string SubscriptionScreen { get; set; }
    }
}
