﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.CompanyRelated;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels
{
    [Table("CueCardsMasters")]
    public class CueCardsMaster
    {
        [Column("Id")]
        public long Id { get; set; }

        [Column("Name")]
        [StringLength(1000)]
        public string Name { get; set; }

        [Column("Description")]
        [StringLength(1000)]
        public string Description { get; set; }

        [Column("CompanyId")]
        public long CompanyId { get; set; }

        public Company Company { get; set; }

        [Column("Sequence")]
        public int Sequence { get; set; }

        [Column("PrimaryType")]
        public CueCardPrimaryType PrimaryType { get; set; }

        [Column("SecondaryType")]
        public CueCardSecondaryType SecondaryType { get; set; }

        [Column("Title")]
        [StringLength(1000)]
        public string Title { get; set; }

        [Column("IsActionable")]
        public bool IsActionable { get; set; }

        [Column("ActionFlow")]
        public CueCardActionFlow? ActionFlow { get; set; }

        [Column("LastUpdatedAt")]
        public DateTime LastUpdatedAt { get; set; }

        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [Column("CreationContext")]
        public string CreationContext { get; set; }

        [Column("IsDeleted")]
        public bool IsDeleted { get; set; }

        [Column("Perspective")]
        public CueCardPerspective Perspective { get; set; }

        [Column("Section")]
        [StringLength(50)]
        public string Section { get; set; }

        public virtual ICollection<OutletMetric> OutletMetrices { get; set; }

        [Column("Header")]
        [StringLength(50)]
        public string Header { get; set; }
    }
}
