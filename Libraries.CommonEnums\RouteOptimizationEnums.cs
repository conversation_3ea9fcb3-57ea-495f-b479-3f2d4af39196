﻿// Copyright (c) FieldAssist. All Rights Reserved.

namespace Libraries.CommonEnums;

public enum PreferredDayOfVisitType
{
    NotRequired = 0,
    BasisOutletMaster = 1,
    BasisHistoricalData = 2,
    WeeklySpecific = 3
}

public enum LocalSearchCoverageStrategy
{
    Off,
    RevisedConditions,
    UniqueCoverage100Percent,
    UniqueWithOutliers100Percent
}

public enum ThresholdConsideration
{
    HomeToHome,
    FirstOutletToLastOutlet
}

public enum RequiredRetailTime
{
    Calculated,
    FlatRetailTime,
    TaskSpecificRetailTime
}

public enum HomeLocationType
{
    SalesRepAttribute,
    HistoricalDayStart,
    MeanOfOutletUniverse
}
