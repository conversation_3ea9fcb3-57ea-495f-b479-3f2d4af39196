﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.SQLDatabase.DBModels.Margin;
using Libraries.CommonEnums;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class DistributorToRetailerMargins
    {
        public long Id { get; set; }

        public long SellerId { get; set; }

        public OutletSegmentation Segmentation { get; set; }

        public long RegionId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        public long ProductId { get; set; }

        public bool Deleted { get; set; }

        public MarginType MarginType { get; set; }

        public float MarginPercentage { get; set; }

        public float? LandingPrice { get; set; }

        public long ZoneId { get; set; }

        public long CompanyId { get; set; }

        public long SellerSegmentationId { get; set; }
    }
}
