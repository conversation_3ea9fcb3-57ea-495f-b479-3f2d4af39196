﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.BaseModels;
using Libraries.CommonEnums;

namespace FA.MasterDB.Core.DTOs
{
    public class DistributorWithGeography : DistributorEntity
    {
        public long? ZoneId { get; set; }
        public string Region { get; set; }
        public string Zone { get; set; }
        public long? DistributorChannelId { get; set; }
        public long? DistributorSegmentationId { get; set; }
        public string? DistributorChannel { get; set; }
        public string? DistributorSegmentation { get; set; }
        public PortalUserRole UserRole { get; set; }
        public StockistType StockistType { get; set; }
    }
}
