﻿// Copyright (c) FieldAssist. All Rights Reserved.

//using System.Data.Entity;
using FA.MasterDB.Core.DTOs;
using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.SQLDatabase.DBConnection;
using FA.MasterDB.SQLDatabase.Mapping;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo
{
    public class ShopTypeRepository : IShopTypeRepository
    {
        private readonly MasterDbContext _sQLMasterDbContext;

        public ShopTypeRepository(MasterDbContext
            sQLMasterDbContext)
        {
            _sQLMasterDbContext = sQLMasterDbContext;
        }

        public async Task<ShopTypesDTO> GetShopTypeById(long shopTypeId, long companyId)
        {
            return await _sQLMasterDbContext.ShopTypes.
                Where(s => s.Id == shopTypeId &&
                s.CompanyId == companyId)
                .Select(s => ShopTypeMapping
                .CreateObject_ShopTypeDTO(s))
                .FirstOrDefaultAsync();
        }
    }
}
