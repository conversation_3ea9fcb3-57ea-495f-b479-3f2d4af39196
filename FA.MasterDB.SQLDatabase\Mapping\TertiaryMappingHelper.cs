﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.DTOs;
using FA.MasterDB.SQLDatabase.DBModels.TertiaryRelated;

namespace FA.MasterDB.SQLDatabase.Mapping
{
    public class TertiaryMappingHelper
    {
        public static TertiaryMinModel GetTertiaryMin(TertiaryEntity tertiary)
        {
            return new TertiaryMinModel
            {
                Id = tertiary.Id,
                Name = tertiary.Name,
                ErpId = tertiary.ErpId,
                Address = tertiary.Address
            };
        }
    }
}
