﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;

namespace FA.MasterDB.SQLDatabase.DBModels.LocationRelated
{
    [Table("Territories")]
    public class Territory
    {
        public string Name { get; set; }
        public string ErpId { set; get; }
        public long Id { get; set; }
        public long CompanyId { get; set; }
        public bool IsDeactive { get; set; }
        public bool Deleted { get; set; }
        public long? RegionId { set; get; }
        public Region Region { get; set; }
    }
}
