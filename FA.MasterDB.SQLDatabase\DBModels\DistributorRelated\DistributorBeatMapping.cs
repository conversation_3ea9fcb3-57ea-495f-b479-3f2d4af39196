﻿// Copyright (c) FieldAssist. All Rights Reserved.

using System.ComponentModel.DataAnnotations.Schema;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;

namespace FA.MasterDB.SQLDatabase.DBModels.DistributorRelated
{
    public class DistributorBeatMapping
    {
        public long Id { get; set; }
        public long DistributorId { get; set; }
        public long BeatId { get; set; }
        public long CompanyId { get; set; }
        public bool Deleted { get; set; }
        public Distributor Distributor { get; set; }
    }

    public class DistributorBeatMappingDB
    {
        public long Id { get; set; }

        public long DistributorId { get; set; }

        [ForeignKey("LocationBeat")]
        public long BeatId { get; set; }

        public DateTime CreatedAt { get; set; }

        public string CreationContext { get; set; }

        [ForeignKey("Company")]
        public long CompanyId { get; set; }

        public bool Deleted { get; set; }

        public Distributor Distributor { get; set; }

        public LocationBeat Beat { get; set; }

        public DateTime LastUpdatedAt { get; set; }
    }
}
