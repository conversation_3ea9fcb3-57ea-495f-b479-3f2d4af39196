﻿// Copyright (c) FieldAssist. All Rights Reserved.

using AutoMapper;
using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.SQLDatabase.DBModels.LocationRelated;
using FA.MasterDB.SQLDatabase.DBModels.Schemes;

namespace FA.MasterDB.SQLDatabase.EntityMapper
{
    public class MasterMappingEntity : Profile
    {
        public MasterMappingEntity()
        {
            CreateMap<Scheme, SchemeEntity>();
            CreateMap<SchemeSlab, SchemeSlabBase>();
            CreateMap<Channel, ChannelEntity>();
        }
    }
}
