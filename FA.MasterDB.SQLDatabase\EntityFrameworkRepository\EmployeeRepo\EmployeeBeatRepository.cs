﻿// Copyright (c) FieldAssist. All Rights Reserved.

using FA.MasterDB.Core.Repositories.IEmployee;
using FA.MasterDB.SQLDatabase.DBConnection;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.EmployeeRepo
{
    public class EmployeeBeatRepository : IEmployeeBeatRepository
    {
        private readonly MasterDbContext dbContext;

        public EmployeeBeatRepository(MasterDbContext dbContext)
        {
            this.dbContext = dbContext;
        }

        public async Task<string?> GetBeat(long companyId, long employeeId)
        {
            return await dbContext.FAEmployeeBeatMappings
                .Where(b => b.CompanyId == companyId && b.EmployeeId == employeeId && !b.IsDeleted)
                .Select(b => b.Beat.Name)
                .FirstOrDefaultAsync();
        }

        public async Task<long> GetEmployeeByBeatId(long companyId, long beatId)
        {
            return await GetEmployeeByBeatIds(companyId,
                    new List<long>() { beatId });
        }

        public async Task<long> GetEmployeeByBeatIds(long companyId, List<long> beatIds)
        {
            return await dbContext.FAEmployeeBeatMappings
                .Where(b => b.CompanyId == companyId && !b.IsDeleted && beatIds.Contains(b.BeatId))
                .Select(b => b.EmployeeId)
                .FirstOrDefaultAsync();
        }

        public async Task<long> GetEmployeeByProductDivisionIds(long companyId, List<long> beatIds, long productDivisionId)
        {
            var employee = await dbContext.EmployeeProductDivisionMappings
                .Where(s => s.ProductDivisionId == productDivisionId && dbContext.FAEmployeeBeatMappings
                    .Any(b => b.CompanyId == companyId && !b.IsDeleted &&
                    beatIds.Contains(b.BeatId) && b.EmployeeId == s.EmployeeId))
                .Select(s => s.EmployeeId)
                .FirstOrDefaultAsync();

            if (employee == default)
            {
                return await GetEmployeeByBeatIds(companyId, beatIds);
            }

            return employee;
        }
    }
}
