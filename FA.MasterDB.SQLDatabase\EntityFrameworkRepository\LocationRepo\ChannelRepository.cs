﻿// Copyright (c) FieldAssist. All Rights Reserved.

using AutoMapper;
using FA.MasterDB.Core.BaseModels;
using FA.MasterDB.Core.Repositories.ILocation;
using FA.MasterDB.SQLDatabase.DBConnection;
using Libraries.CommonEnums;
using Microsoft.EntityFrameworkCore;

namespace FA.MasterDB.SQLDatabase.EntityFrameworkRepository.LocationRepo
{
    public class ChannelRepository : IChannelRepository
    {
        private readonly MasterDbContext _masterDbContext;
        private readonly IMapper _mapper;

        public ChannelRepository(MasterDbContext masterDbContext
            , IMapper mapper)
        {
            _masterDbContext = masterDbContext;
            _mapper = mapper;
        }

        public async Task<ChannelEntity> GetOutletChannelEntityMin(long companyId,
            OutletChannel outletChannel)
        {
            var record = await _masterDbContext.Channels.
                Where(s => s.CompanyId == companyId && s.IsValid && s.Enum == outletChannel)
                .Select(s => _mapper.Map<ChannelEntity>(s))
                .FirstOrDefaultAsync();

            return record;
        }
    }
}
